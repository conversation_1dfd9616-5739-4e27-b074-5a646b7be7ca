@php
  $current_route_name = Route::currentRouteName();
  if ($current_route_name && app()->getLocale() == 'en') {
    $en_route_name = $current_route_name;
    $ar_route_name = str_replace('.en', '.ar', $en_route_name);
  } else if ($current_route_name && app()->getLocale() == 'ar') {
    $ar_route_name = $current_route_name;
    $en_route_name = str_replace('.ar', '.en', $ar_route_name);
  } else {
    // Fallback routes when no current route exists
    $en_route_name = 'index.en';
    $ar_route_name = 'index.ar';
  }
@endphp
@if(str_contains(url()->current(), 'advancedsearch'))
  <div class="mt-5 noprint"></div>
@else
  <div class="header-back noprint"></div>
  <header class="noprint">
    <div class="header-logo">
    <a href="https://www.banquezitouna.com/">
      <img src="/assets/img/logo.png" width="149" height="44" alt="مصرف الزيتونة" typeof="foaf:Image" />
    </a>
    </div>

    <nav class="navbar navbar-expand-lg custom-toggler costumized-navbar">
    <div class="container-fluid">
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
      aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
      <!-- <span class="navbar-toggler-icon"></span> -->
      <span class='hamburger-menu'>
        <span class='burger-1'></span>
        <span class='burger-2'></span>
        <span class='burger-3'></span>
      </span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNavDropdown">
      <ul class="navbar-nav" style="width: 100%;justify-content: space-around;text-align: center;">

        <li class="nav-item">
        <a class="nav-link {{ request()->path() === '/' || Request::is(app()->getlocale()) || Str::startsWith(Request::path(), app()->getlocale() . '/search') || Str::startsWith(Request::path(), 'search') ? 'selected-nav' : '' }}"
          href="{{route('index.' . app()->getlocale())}}">{{__('homepage')}}</a>
        </li>
        <li class="nav-item">
        <a class="nav-link {{ Request::is(app()->getlocale() . '/about') || Request::is('about') ? 'selected-nav' : '' }}"
          href="{{route('aboutus.' . app()->getlocale())}}">{{__('aboutproject')}}</a>
        </li>
        <li class="nav-item">
        <a class="nav-link {{ Request::is(app()->getlocale() . '/aboutdatabase') || Request::is('aboutdatabase') ? 'selected-nav' : '' }}"
          href="{{route('aboutdatabase.' . app()->getlocale())}}">{{__('aboutdatabase')}}</a>
        </li>
        <li class="nav-item">
        <a class="nav-link {{ Request::is(app()->getlocale() . '/userguide') || Request::is('userguide') ? 'selected-nav' : '' }}"
          href="{{route('userguide.' . app()->getlocale())}}">{{__('userguide')}}</a>
        </li>
        <li class="nav-item">
        <a class="nav-link {{ Str::startsWith(Request::path(), app()->getlocale() . '/news') || Str::startsWith(Request::path(), 'news') ? 'selected-nav' : '' }}"
          href="{{route('news.' . app()->getlocale())}}">{{__('news')}}</a>
        </li>
        <li class="nav-item">
        <a class="nav-link {{ Str::startsWith(Request::path(), app()->getlocale() . '/contact') || Str::startsWith(Request::path(), 'contact') ? 'selected-nav' : '' }}"
          href="{{route('contact.' . app()->getlocale())}}">{{__('contact')}}</a>
        </li>
        <li class="nav-item-lang">
        @if(Route::current())
        @if(app()->getlocale() == "en") <a class="navbar-language-link" @selected(true)
      href="{{ route($ar_route_name, Route::current()->parameters()) }}">{{__('العربية')}}</a> @endif
        @if(app()->getlocale() == "ar") <a class="navbar-language-link" @selected(true)
      href="{{ route($en_route_name, Route::current()->parameters()) }}">{{__('English')}}</a> @endif
      @else
        @if(app()->getlocale() == "en") <a class="navbar-language-link" @selected(true)
      href="{{ route('index.ar') }}">{{__('العربية')}}</a> @endif
        @if(app()->getlocale() == "ar") <a class="navbar-language-link" @selected(true)
      href="{{ route('index.en') }}">{{__('English')}}</a> @endif
      @endif
        </li>
      </ul>
      </div>
    </div>
    </nav>
    <div class="language">
    @include('components.languageselector')
    </div>
  </header>
@endif