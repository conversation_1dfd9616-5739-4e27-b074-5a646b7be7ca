@php
    $current_route_name = Route::currentRouteName();
    if ($current_route_name && app()->getLocale() == 'en') {
        $en_route_name = $current_route_name;
        $ar_route_name = str_replace('.en', '.ar', $en_route_name);
    } else if ($current_route_name && app()->getLocale() == 'ar') {
        $ar_route_name = $current_route_name;
        $en_route_name = str_replace('.ar', '.en', $ar_route_name);
    } else {
        // Fallback routes when no current route exists
        $en_route_name = 'index.en';
        $ar_route_name = 'index.ar';
    }
@endphp

<!-- @if(app()->getlocale()=="ar") <div class="dropdown" style="box-shadow: 1px 0px #4a7d07;padding-right: 20px;">@endif
@if(app()->getlocale()=="en") <div class="dropdown" style="box-shadow: -1px 0px #4a7d07;padding-left: 20px;">@endif -->
<div class="dropdown">
    <svg width="26" height="25" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="pb-1">
        <g opacity="0.7">
            <path
                d="M8.5 15.0625C12.3833 15.0625 15.5312 11.9145 15.5312 8.03125C15.5312 4.148 12.3833 1 8.5 1C4.61675 1 1.46875 4.148 1.46875 8.03125C1.46875 11.9145 4.61675 15.0625 8.5 15.0625Z"
                stroke="#5c5c5c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path
                d="M5.42439 10.375C5.56686 10.375 5.70745 10.3425 5.83549 10.28C5.96352 10.2175 6.07563 10.1267 6.1633 10.0144C6.25096 9.90205 6.31188 9.77124 6.34142 9.63187C6.37095 9.49249 6.36834 9.34821 6.33376 9.21L5.86501 7.335C5.81429 7.13221 5.69724 6.95219 5.53247 6.82355C5.36771 6.69491 5.16467 6.62502 4.95564 6.625H1.61001C1.3515 7.89491 1.44911 9.21158 1.89207 10.4295C2.33502 11.6474 3.10608 12.7191 4.12001 13.5263L4.75001 10.375H5.42439Z"
                stroke="#5c5c5c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            <path
                d="M14.1156 3.8125H11.5756C11.3666 3.81252 11.1636 3.88241 10.9988 4.01105C10.834 4.13969 10.717 4.31971 10.6663 4.5225L10.1975 6.3975C10.1629 6.53571 10.1603 6.67999 10.1899 6.81936C10.2194 6.95874 10.2803 7.08955 10.368 7.20186C10.4556 7.31417 10.5678 7.40502 10.6958 7.46751C10.8238 7.53 10.9644 7.56248 11.1069 7.5625H12.0938L12.5881 10.5312C12.6247 10.7501 12.7377 10.949 12.9071 11.0924C13.0765 11.2358 13.2912 11.3144 13.5131 11.3144H14.7163C15.3406 10.1368 15.6157 8.80541 15.5093 7.47682C15.4029 6.14822 14.9194 4.87761 14.1156 3.81437V3.8125Z"
                stroke="#5c5c5c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </g>
    </svg>

    <!-- <select onchange="window.location.href=this.value" style="background: transparent;color: #000000;">
        <option disabled>{{__('language')}}</option>
        <option @if(app()->getlocale()=="ar") @selected(true) @endif value="{{ route($ar_route_name, Route::current() ? Route::current()->parameters() : []) }}">{{__('العربية')}}</option>
        <option @if(app()->getlocale()=="en") @selected(true) @endif value="{{ route($en_route_name, Route::current() ? Route::current()->parameters() : []) }}">{{__('English')}}</option>

    </select> -->
    @if(Route::current())
        @if(app()->getlocale() == "en") <a class="language-link" @selected(true)
        href="{{ route($ar_route_name, Route::current()->parameters()) }}">{{__('العربية')}}</a> @endif
        @if(app()->getlocale() == "ar") <a class="language-link" @selected(true)
        href="{{ route($en_route_name, Route::current()->parameters()) }}">{{__('English')}}</a> @endif
    @else
        @if(app()->getlocale() == "en") <a class="language-link" @selected(true)
        href="{{ route('index.ar') }}">{{__('العربية')}}</a> @endif
        @if(app()->getlocale() == "ar") <a class="language-link" @selected(true)
        href="{{ route('index.en') }}">{{__('English')}}</a> @endif
    @endif
</div>