<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Example Email via Microsoft Graph</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .footer {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Example Email via Microsoft Graph</h1>
        <p>This email was sent using the Microsoft Graph mail driver.</p>
    </div>

    <div class="content">
        @if($user)
            <p>Hello {{ $user->name ?? 'User' }},</p>
        @else
            <p>Hello,</p>
        @endif

        <p>This is an example email sent through the Microsoft Graph API integration.</p>

        @if($content)
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <h3>Message Content:</h3>
                <p>{{ $content }}</p>
            </div>
        @endif

        <p>The Microsoft Graph mail driver provides the following features:</p>
        <ul>
            <li>OAuth 2.0 authentication with Microsoft Graph</li>
            <li>Support for HTML and text emails</li>
            <li>Attachment support</li>
            <li>CC and BCC recipients</li>
            <li>Reply-to addresses</li>
            <li>Automatic token caching and refresh</li>
        </ul>

        <p>Best regards,<br>
        {{ config('app.name') }}</p>
    </div>

    <div class="footer">
        <p>This email was sent from {{ config('app.name') }} using Microsoft Graph API.</p>
        <p>Sent at: {{ now()->format('Y-m-d H:i:s') }}</p>
    </div>
</body>
</html>
