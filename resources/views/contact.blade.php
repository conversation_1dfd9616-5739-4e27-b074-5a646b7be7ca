@extends('layout')

@section('content')
    <style>
        /* Simple Success Alert */
        .alert-success {
            background-color: #d1fae5;
            border-color: #10b981;
            color: #065f46;
            border-radius: 8px;
            padding: 1rem;
            border-left: 4px solid #10b981;
            margin-bottom: 1.5rem;
        }

        /* Enhanced Field Validation - Override ALL classes including search-item and object */
        .form-control.is-invalid,
        input.form-control.is-invalid,
        textarea.form-control.is-invalid,
        select.form-control.is-invalid,
        .search-item.form-control.is-invalid,
        .object.form-control.is-invalid,
        .col-12.search-item.form-control.is-invalid,
        .col-12.search-item.object.form-control.is-invalid {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
            background-color: #fef2f2 !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
            background-repeat: no-repeat !important;
            background-position: right calc(0.375em + 0.1875rem) center !important;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
            padding-right: calc(1.5em + 0.75rem) !important;
        }

        .form-control.is-invalid:focus,
        input.form-control.is-invalid:focus,
        textarea.form-control.is-invalid:focus,
        select.form-control.is-invalid:focus,
        .search-item.form-control.is-invalid:focus,
        .object.form-control.is-invalid:focus,
        .col-12.search-item.form-control.is-invalid:focus,
        .col-12.search-item.object.form-control.is-invalid:focus {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
            background-color: #fff !important;
        }

        /* RTL Support for input validation icons */
        [dir="rtl"] .form-control.is-invalid,
        [dir="rtl"] input.form-control.is-invalid,
        [dir="rtl"] textarea.form-control.is-invalid,
        [dir="rtl"] select.form-control.is-invalid,
        [dir="rtl"] .search-item.form-control.is-invalid,
        [dir="rtl"] .object.form-control.is-invalid,
        [dir="rtl"] .col-12.search-item.form-control.is-invalid,
        [dir="rtl"] .col-12.search-item.object.form-control.is-invalid {
            background-position: left calc(0.375em + 0.1875rem) center !important;
            padding-right: 0.75rem !important;
            padding-left: calc(1.5em + 0.75rem) !important;
        }

        /* Consistent validation error styling for ALL fields */
        small.text-danger,
        .text-danger {
            color: #dc3545 !important;
            font-size: 0.875rem !important;
            margin-top: 0.5rem !important;
            margin-bottom: 0 !important;
            display: flex !important;
            align-items: center !important;
            font-weight: 500 !important;
            padding: 0.5rem 0.75rem !important;
            background-color: #fef2f2 !important;
            border-left: 3px solid #ef4444 !important;
            border-radius: 0 6px 6px 0 !important;
            animation: slideIn 0.3s ease-out !important;
            width: 100% !important;
            box-sizing: border-box !important;
            font-family: inherit !important;
            line-height: 1.4 !important;
        }

        small.text-danger::before,
        .text-danger::before {
            content: "⚠" !important;
            margin-right: 0.5rem !important;
            font-size: 1rem !important;
            color: #dc3545 !important;
            flex-shrink: 0 !important;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* RTL Support for field errors */
        [dir="rtl"] small.text-danger,
        [dir="rtl"] .text-danger {
            border-left: none !important;
            border-right: 3px solid #ef4444 !important;
            border-radius: 6px 0 0 6px !important;
        }

        [dir="rtl"] small.text-danger::before,
        [dir="rtl"] .text-danger::before {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
        }

        /* Button Loading State */
        .btn-loading {
            pointer-events: none;
            opacity: 0.7;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .alert-success {
                margin: 0 0.5rem 1.5rem 0.5rem;
                padding: 0.875rem 1rem;
            }
        }
    </style>
    <div class="row">
        <div class="d-flex justify-content-end mt-3"></div>
    </div>

    <form id="contactForm" action="{{ route('storecontact.' . app()->getlocale()) }}" method="post">
        @csrf
        <div class="row form-contact">
            <div class="row ml-8 mr-8" id="advanced-search">
                <div class="row">
                    <div class="row col-md-12 mr-2">
                        <div class="col-md-2 title-advanced-search">
                            {!! __('contactformtitle') !!}
                        </div>
                        <div class="col-md-10 title-bar mb-2"></div>
                        <div class="col-md-12">
                            {{-- Success message only --}}
                            @if (\Session::has('message'))
                                <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>{{ __('validation.تم بنجاح!') }}</strong>
                                    {{ __(\Session::get('message')) }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                        </div>
                    </div>

                    @if (!\Session::has('message'))
                        <div class="col-md-2 col-ms-12"></div>
                        <div class="col-md-8 col-ms-12">
                            {{-- Name --}}
                            <div class="col-md-12 mt-5">
                                <input type="text" class="col-12 search-item form-control @error('name') is-invalid @enderror"
                                    name="name" id="name" placeholder="{{ __('name') }}" value="{{ old('name') }}">
                                @error('name')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Email --}}
                            <div class="col-md-12 mt-5">
                                <input type="email" class="col-12 search-item form-control @error('email') is-invalid @enderror"
                                    name="email" id="email" placeholder="{{ __('email') }}" value="{{ old('email') }}">
                                @error('email')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Country --}}
                            <div class="col-md-12 mt-5">
                                <input type="text" class="col-12 search-item form-control @error('pays') is-invalid @enderror"
                                    name="pays" id="pays" placeholder="{{ __('country') }}" value="{{ old('pays') }}">
                                @error('pays')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Institution --}}
                            <div class="col-md-12 mt-5">
                                <input type="text"
                                    class="col-12 search-item form-control @error('institution') is-invalid @enderror"
                                    name="institution" id="institution" placeholder="{{ __('institution') }}"
                                    value="{{ old('institution') }}">
                                @error('institution')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Phone --}}
                            <div class="col-md-12 mt-5">
                                <input type="tel" class="col-12 search-item form-control @error('phone') is-invalid @enderror"
                                    name="phone" id="phone" placeholder="{{ __('phone') }}" value="{{ old('phone') }}"
                                    style="direction: ltr; @if (app()->getlocale() == 'ar') text-align: end; @endif">
                                @error('phone')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Subject --}}
                            <div class="col-md-12 mt-5">
                                <input type="text" class="col-12 search-item form-control @error('sujet') is-invalid @enderror"
                                    name="sujet" id="sujet" placeholder="{{ __('subject') }}" value="{{ old('sujet') }}">
                                @error('sujet')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Message --}}
                            <div class="col-md-12 mt-5 p-8">
                                <textarea name="object" id="object"
                                    class="col-12 search-item object form-control @error('object') is-invalid @enderror"
                                    placeholder="{{ __('message') }}">@if (Request::get('reportarticleerror')){{ Request::get('reportarticleerror') }}@else{{ old('object') }}@endif</textarea>
                                @error('object')
                                    <small class="text-danger">{{ $message }}</small>
                                @enderror
                            </div>

                            {{-- Submit Button --}}
                            <div class="d-flex justify-content-center col-md-12 mt-5">
                                <button type="submit" class="button">{{ __('sendcontact') }}</button>
                            </div>
                    @endif
                    </div>
                    <div class="col-md-2 col-ms-12"></div>
                </div>
            </div>
        </div>
    </form>

    {{-- Form Enhancement Script --}}
    <script>
        // Client-side validation to prevent empty form submissions
        function validateContactForm() {
            const requiredFields = ['name', 'email', 'pays', 'institution', 'phone', 'sujet', 'object'];
            let isValid = true;
            let firstInvalidField = null;

            // Clear previous validation states
            requiredFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field) {
                    field.classList.remove('is-invalid');
                    const errorElement = field.parentNode.querySelector('.text-danger');
                    if (errorElement) {
                        errorElement.remove();
                    }
                }
            });

            // Validate each required field
            requiredFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && (!field.value || field.value.trim() === '')) {
                    isValid = false;
                    field.classList.add('is-invalid');

                    // Add error message
                    const errorDiv = document.createElement('small');
                    errorDiv.className = 'text-danger';
                    errorDiv.textContent = '{{ __("validation.required", ["attribute" => ""]) }}'.replace(':attribute', field.placeholder);
                    field.parentNode.appendChild(errorDiv);

                    if (!firstInvalidField) {
                        firstInvalidField = field;
                    }
                }
            });

            // Email validation
            const emailField = document.getElementById('email');
            if (emailField && emailField.value.trim() !== '') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(emailField.value)) {
                    isValid = false;
                    emailField.classList.add('is-invalid');
                    const errorDiv = document.createElement('small');
                    errorDiv.className = 'text-danger';
                    errorDiv.textContent = '{{ __("validation.email") }}';
                    emailField.parentNode.appendChild(errorDiv);

                    if (!firstInvalidField) {
                        firstInvalidField = emailField;
                    }
                }
            }

            // Phone validation
            const phoneField = document.getElementById('phone');
            if (phoneField && phoneField.value.trim() !== '') {
                const phoneRegex = /^[\+]?[0-9\s\-\(\)\.]+$/;
                if (!phoneRegex.test(phoneField.value)) {
                    isValid = false;
                    phoneField.classList.add('is-invalid');
                    const errorDiv = document.createElement('small');
                    errorDiv.className = 'text-danger';
                    errorDiv.textContent = '{{ __("validation.phone_regex") }}';
                    phoneField.parentNode.appendChild(errorDiv);

                    if (!firstInvalidField) {
                        firstInvalidField = phoneField;
                    }
                }
            }

            // Focus on first invalid field
            if (firstInvalidField) {
                firstInvalidField.focus();
                firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            return isValid;
        }

        // Add loading state to submit button with validation
        document.getElementById('contactForm').addEventListener('submit', function (event) {
            // Prevent submission if validation fails
            if (!validateContactForm()) {
                event.preventDefault();
                return false;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{{ __("sending") }}...';
            submitBtn.disabled = true;

            // Re-enable button after 10 seconds in case of issues
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });

        // Real-time validation on field blur
        document.addEventListener('DOMContentLoaded', function () {
            const requiredFields = ['name', 'email', 'pays', 'institution', 'phone', 'sujet', 'object'];

            requiredFields.forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field) {
                    field.addEventListener('blur', function () {
                        // Remove previous error state
                        this.classList.remove('is-invalid');
                        const errorElement = this.parentNode.querySelector('.text-danger');
                        if (errorElement) {
                            errorElement.remove();
                        }

                        // Validate field
                        if (!this.value || this.value.trim() === '') {
                            this.classList.add('is-invalid');
                            const errorDiv = document.createElement('small');
                            errorDiv.className = 'text-danger';
                            errorDiv.textContent = '{{ __("validation.required", ["attribute" => ""]) }}'.replace(':attribute', this.placeholder);
                            this.parentNode.appendChild(errorDiv);
                        }
                    });
                }
            });
        });

        // Auto-hide alerts after 10 seconds
        document.addEventListener('DOMContentLoaded', function () {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert && alert.classList.contains('show')) {
                        alert.classList.remove('show');
                        setTimeout(() => alert.remove(), 150);
                    }
                }, 10000);
            });
        });
    </script>
@endsection