APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

# Microsoft Graph Mail Configuration
# To use Microsoft Graph for email, set MAIL_MAILER=microsoft-graph
# and configure the following settings from your Azure App Registration:
MICROSOFT_GRAPH_TENANT_ID=your-tenant-id
MICROSOFT_GRAPH_CLIENT_ID=your-client-id
MICROSOFT_GRAPH_CLIENT_SECRET=your-client-secret

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SCOUT_DRIVER=Matchish\ScoutElasticSearch\Engines\ElasticSearchEngine
ELASTICSEARCH_HOST=127.0.0.1:9200


DEFAULT_API_AUTHORIZATION_BRIDGE = 'eyJpdiI6ImJKNGUzaUhheWJLUDdTcnhTQ2YwMWc9PSIsInZhbHVlIjoiQi9sOXJXaGFubklhalJWQmVIWnIreG5PTSt3dURWelNJdWJFcDNBL25vQ1JSMG05Vm0rVnJvb1huUXZ3bUticVEyT1BhRERwdWlUampJV0NiYlZHWnE0MU1pOWNrRy82c2dMbG5MMktXSytxVHB0ZW5DSWlwWEt3Qm1MOUZ3TlMiLCJtYWMiOiIyNjg5MTQ5YWM4ODAwNTI3N2ZmYjk3NjNjZTY2YjJkODM2ZjU2ZTFjYmE3MzJmZTYxMzg0ZGU3MGJhNzRiMmRlIiwidGFnIjoiIn0'
API_IMPORTED_ARTICLE_EXCELFILE='api/imported-article-excelfile'
MAIL_FROM_ADDRESS="<EMAIL>"
