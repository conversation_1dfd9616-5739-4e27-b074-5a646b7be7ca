<?php

return [
    /*
    |--------------------------------------------------------------------------
    | XSS Protection Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for XSS protection including
    | allowed HTML tags, CSP settings, and security policies.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Allowed HTML Tags
    |--------------------------------------------------------------------------
    |
    | Define which HTML tags are allowed in user content.
    | This is used by HTML Purifier for cleaning content.
    |
    */
    'allowed_html' => 'p,br,strong,b,em,i,u,ol,ul,li,a[href|title],img[src|alt|width|height|title],h1,h2,h3,h4,h5,h6,blockquote,code,pre',

    /*
    |--------------------------------------------------------------------------
    | Allowed HTML Tags (Basic)
    |--------------------------------------------------------------------------
    |
    | Basic HTML tags allowed for simple content areas.
    |
    */
    'allowed_tags' => '<p><br><strong><b><em><i><u><ol><ul><li><a><img><h1><h2><h3><h4><h5><h6>',

    /*
    |--------------------------------------------------------------------------
    | Content Security Policy Settings
    |--------------------------------------------------------------------------
    |
    | Configure Content Security Policy directives.
    |
    */
    'csp' => [
        'default_src' => "'self'",
        'script_src' => "'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://www.googletagmanager.com https://www.google-analytics.com",
        'style_src' => "'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'font_src' => "'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
        'img_src' => "'self' data: https: blob:",
        'media_src' => "'self' https:",
        'object_src' => "'none'",
        'base_uri' => "'self'",
        'form_action' => "'self'",
        'frame_ancestors' => "'none'",
        'upgrade_insecure_requests' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed Redirect Hosts
    |--------------------------------------------------------------------------
    |
    | Define which hosts are allowed for redirects to prevent open redirects.
    |
    */
    'allowed_redirect_hosts' => [
        'islamicfinance.tn',
        'www.islamicfinance.tn',
        'localhost',
        '127.0.0.1',
    ],

    /*
    |--------------------------------------------------------------------------
    | Rich Text Editor Settings
    |--------------------------------------------------------------------------
    |
    | Settings specific to rich text editors like Tiptap.
    |
    */
    'rich_text' => [
        'allowed_html' => 'p,br,strong,b,em,i,u,ol,ul,li,a[href|title],img[src|alt|width|height|title],h1,h2,h3,h4,h5,h6,blockquote,code,pre,table,thead,tbody,tr,th,td',
        'max_length' => 50000, // Maximum length for rich text content
        'allow_iframes' => false,
        'allowed_iframe_domains' => [
            'www.youtube.com',
            'player.vimeo.com',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Input Sanitization Rules
    |--------------------------------------------------------------------------
    |
    | Define sanitization rules for different input types.
    |
    */
    'sanitization_rules' => [
        'title' => [
            'allow_html' => false,
            'strict_mode' => true,
            'max_length' => 255,
        ],
        'description' => [
            'allow_html' => true,
            'strict_mode' => false,
            'max_length' => 10000,
        ],
        'content' => [
            'allow_html' => true,
            'strict_mode' => false,
            'max_length' => 50000,
        ],
        'name' => [
            'allow_html' => false,
            'strict_mode' => true,
            'max_length' => 100,
        ],
        'email' => [
            'allow_html' => false,
            'strict_mode' => true,
            'max_length' => 255,
        ],
        'url' => [
            'allow_html' => false,
            'strict_mode' => true,
            'max_length' => 2048,
        ],
        'search' => [
            'allow_html' => false,
            'strict_mode' => true,
            'max_length' => 500,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    |
    | Additional security headers to prevent XSS and other attacks.
    |
    */
    'security_headers' => [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin',
        'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    |
    | Configure logging for XSS protection events.
    |
    */
    'logging' => [
        'log_xss_attempts' => true,
        'log_channel' => 'security',
        'log_level' => 'warning',
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Settings
    |--------------------------------------------------------------------------
    |
    | Settings for input validation and XSS detection.
    |
    */
    'validation' => [
        'strict_mode' => env('XSS_STRICT_MODE', false),
        'auto_clean' => env('XSS_AUTO_CLEAN', true),
        'block_suspicious_content' => env('XSS_BLOCK_SUSPICIOUS', false), // Changed to false for better UX
        'sanitize_instead_of_block' => env('XSS_SANITIZE_INSTEAD_BLOCK', true), // New option
    ],

    /*
    |--------------------------------------------------------------------------
    | Whitelist Settings
    |--------------------------------------------------------------------------
    |
    | Define whitelisted patterns and content that should bypass XSS filtering.
    |
    */
    'whitelist' => [
        'admin_users' => [], // User IDs that can bypass some XSS filtering
        'trusted_domains' => [
            'islamicfinance.tn',
            'www.islamicfinance.tn',
        ],
        'safe_patterns' => [
            // Patterns that are considered safe and should not trigger XSS warnings
        ],
    ],
];
