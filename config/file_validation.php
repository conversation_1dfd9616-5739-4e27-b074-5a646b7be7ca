<?php

return [
    /*
    |--------------------------------------------------------------------------
    | File Upload Validation Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for file upload validation
    | including allowed file types, maximum sizes, and security settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Maximum File Sizes
    |--------------------------------------------------------------------------
    |
    | Define maximum file sizes for different types of uploads.
    | Sizes are in bytes.
    |
    */
    'max_sizes' => [
        'default' => 50 * 1024 * 1024, // 50MB
        'image' => 10 * 1024 * 1024,   // 10MB
        'avatar' => 5 * 1024 * 1024,   // 5MB
        'trix' => 10 * 1024 * 1024,    // 10MB
        'document' => 25 * 1024 * 1024, // 25MB
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed File Extensions
    |--------------------------------------------------------------------------
    |
    | Define which file extensions are allowed for uploads.
    |
    */
    'allowed_extensions' => [
        'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
        'documents' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv'],
        'archives' => ['zip', 'rar', '7z'],
        'videos' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
        'audio' => ['mp3', 'wav', 'ogg', 'aac', 'flac'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Dangerous File Extensions
    |--------------------------------------------------------------------------
    |
    | File extensions that should never be allowed for security reasons.
    |
    */
    'dangerous_extensions' => [
        'php', 'php3', 'php4', 'php5', 'phtml', 'phps',
        'js', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr',
        'vbs', 'vbe', 'jse', 'ws', 'wsf', 'wsc', 'wsh',
        'ps1', 'ps1xml', 'ps2', 'ps2xml', 'psc1', 'psc2',
        'msh', 'msh1', 'msh2', 'mshxml', 'msh1xml', 'msh2xml',
        'scf', 'lnk', 'inf', 'reg', 'asp', 'aspx', 'jsp',
        'jar', 'war', 'ear', 'class', 'py', 'rb', 'pl',
        'sh', 'bash', 'zsh', 'fish', 'csh', 'tcsh'
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed MIME Types
    |--------------------------------------------------------------------------
    |
    | Define which MIME types are allowed for uploads.
    |
    */
    'allowed_mime_types' => [
        // Images
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
        'image/webp', 'image/svg+xml',
        
        // Documents
        'application/pdf',
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint', 
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain', 'text/rtf', 'text/csv',
        
        // Archives
        'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
        
        // Videos
        'video/mp4', 'video/x-msvideo', 'video/quicktime', 
        'video/x-ms-wmv', 'video/x-flv', 'video/webm',
        
        // Audio
        'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac'
    ],

    /*
    |--------------------------------------------------------------------------
    | Dangerous MIME Types
    |--------------------------------------------------------------------------
    |
    | MIME types that should never be allowed for security reasons.
    |
    */
    'dangerous_mime_types' => [
        'application/x-php',
        'application/x-httpd-php',
        'application/php',
        'text/php',
        'text/x-php',
        'application/x-sh',
        'application/x-csh',
        'text/x-script.sh',
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program'
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Additional security settings for file uploads.
    |
    */
    'security' => [
        'scan_content' => true,
        'check_file_headers' => true,
        'block_double_extensions' => true,
        'quarantine_suspicious_files' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Messages
    |--------------------------------------------------------------------------
    |
    | Custom error messages for file validation failures.
    |
    */
    'error_messages' => [
        'dangerous_extension' => 'The uploaded file type is not allowed for security reasons.',
        'invalid_extension' => 'The uploaded file extension is not allowed.',
        'file_too_large' => 'The uploaded file is too large. Maximum size is :max_size.',
        'dangerous_mime' => 'The uploaded file MIME type is not allowed for security reasons.',
        'invalid_mime' => 'The uploaded file MIME type is not allowed.',
        'suspicious_content' => 'Suspicious file content detected.',
        'invalid_filename' => 'Invalid file name detected.',
    ],
];
