<?php

namespace App\Observers;

use App\Services\XSSProtectionService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class XSSProtectionObserver
{
    /**
     * Fields that should be sanitized for XSS
     */
    protected $fieldsToSanitize = [
        'title', 'subtitle', 'name', 'description', 'content', 'body',
        'summary', 'excerpt', 'bio', 'about', 'message', 'comment',
        'note', 'remarks', 'details', 'text'
    ];

    /**
     * Fields that allow HTML content
     */
    protected $htmlFields = [
        'description', 'content', 'body', 'bio', 'about', 'details'
    ];

    /**
     * Handle the model "creating" event.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function creating(Model $model)
    {
        $this->sanitizeModelAttributes($model);
    }

    /**
     * Handle the model "updating" event.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function updating(Model $model)
    {
        $this->sanitizeModelAttributes($model);
    }

    /**
     * Sanitize model attributes for XSS
     *
     * @param Model $model
     * @return void
     */
    protected function sanitizeModelAttributes(Model $model): void
    {
        $attributes = $model->getAttributes();
        $dirty = $model->getDirty();

        foreach ($dirty as $field => $value) {
            if ($this->shouldSanitizeField($field) && is_string($value)) {
                $sanitized = $this->sanitizeField($field, $value, $model);
                
                if ($sanitized !== $value) {
                    $model->setAttribute($field, $sanitized);
                    
                    // Log the sanitization
                    Log::info('XSS Protection: Content sanitized', [
                        'model' => get_class($model),
                        'field' => $field,
                        'original_length' => strlen($value),
                        'sanitized_length' => strlen($sanitized),
                        'user_id' => auth()->id()
                    ]);
                }
            }
        }
    }

    /**
     * Determine if a field should be sanitized
     *
     * @param string $field
     * @return bool
     */
    protected function shouldSanitizeField(string $field): bool
    {
        // Check if field is in the list of fields to sanitize
        foreach ($this->fieldsToSanitize as $pattern) {
            if (str_contains($field, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Sanitize a specific field
     *
     * @param string $field
     * @param string $value
     * @param Model $model
     * @return string
     */
    protected function sanitizeField(string $field, string $value, Model $model): string
    {
        if (empty($value)) {
            return $value;
        }

        // Check if this field allows HTML
        $allowHtml = $this->fieldAllowsHtml($field);

        if ($allowHtml) {
            // For HTML fields, use rich text sanitization
            return XSSProtectionService::sanitizeRichText($value);
        } else {
            // For text fields, use basic sanitization
            return XSSProtectionService::sanitizeInput($value, [
                'allow_html' => false,
                'strict_mode' => true
            ]);
        }
    }

    /**
     * Check if a field allows HTML content
     *
     * @param string $field
     * @return bool
     */
    protected function fieldAllowsHtml(string $field): bool
    {
        foreach ($this->htmlFields as $pattern) {
            if (str_contains($field, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the fields that should be sanitized for a specific model
     *
     * @param Model $model
     * @return array
     */
    protected function getModelSanitizationRules(Model $model): array
    {
        $modelClass = get_class($model);

        // Define model-specific sanitization rules
        $rules = [
            'App\Models\News' => [
                'title' => ['allow_html' => false, 'strict_mode' => true],
                'subtitle' => ['allow_html' => false, 'strict_mode' => true],
                'description' => ['allow_html' => true, 'strict_mode' => false],
            ],
            'App\Models\User' => [
                'name' => ['allow_html' => false, 'strict_mode' => true],
                'email' => ['allow_html' => false, 'strict_mode' => true],
                'bio' => ['allow_html' => true, 'strict_mode' => false],
            ],
            'App\Models\Contact' => [
                'name' => ['allow_html' => false, 'strict_mode' => true],
                'email' => ['allow_html' => false, 'strict_mode' => true],
                'subject' => ['allow_html' => false, 'strict_mode' => true],
                'message' => ['allow_html' => false, 'strict_mode' => true],
            ],
        ];

        return $rules[$modelClass] ?? [];
    }
}
