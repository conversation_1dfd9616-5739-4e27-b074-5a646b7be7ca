<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;
use <PERSON>tie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\SoftDeletes;


class InterConference extends Model
{
    use HasFactory;
    use Searchable;
    use HasSlug;
    use SoftDeletes;
            /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }
    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected $fillable = [
        'code',
        'organiser',
        'sourcegroup_id',
        'conference_title',
        'year',
        'session_number',
        'session_title',
        'research_number',
        'title', 'author',
        'chapter_number',
        'chapter_title',
        'search_number',
        'search_title',
        'request_number',
        'request_title',
        'secondary_title_one_number',
        'secondary_title_one',
        'secondary_title_two_number',
        'secondary_title_two',
        'page_number',
        'source_number',
        'language_id',
        'comments','created_at','updated_at'
    ];


    public function getClassname(){
        return 'InterConference';
    }
    public function getTitle(){
        return $this->title;
    }
    public function getType(){
        return '';
    }
    public function article_second_title(){
        return 'conference_title';
    }
    public function article_content_second_title(){
        return $this->author;
    }
    public function article_content_extend_title(){
        return $this->secondary_title_one;
    }
    public function article_third_title(){
        return $this->organiser;
    }
    public function article_content_third_title(){
        return $this->year;
    }
    public function getexporter(){
        return $this->organiser;
    }

    /**/
    public function getcode(){
        return $this->code;
    }
    public function getauthor(){
        return $this->author;
    }
    public function getyear(){
        return $this->year;
    }
    public function getfolder_number(){
        return $this->folder_number;
    }
    public function getact_number(){
        return $this->act_number;
    }
    public function getorganiser(){
        return $this->organiser;
    }
    public function getconference_title(){
        return $this->conference_title;
    }
    public function getact_title(){
        return $this->act_title;
    }
    public function getchapter_number(){
        return $this->chapter_number;
    }
    public function getchapter_title(){
        return $this->chapter_title;
    }
    public function getserial_number(){
        return '';
    }
    public function getsession_number(){
        return $this->session_number;
    }
    public function getmain_title_number(){
        return '';
    }
    public function getsearch_number(){
        return $this->search_number;
    }
    public function getsearch_title(){
        return $this->search_title;
    }
    public function getrequest_number(){
        return $this->request_number;
    }
    public function getrequest_title(){
        return $this->request_title;
    }
    public function getsecondary_title_one_number(){
        return $this->secondary_title_one_number;
    }
    public function getsecondary_title_one(){
        return $this->secondary_title_one;
    }
    public function getsecondary_title_two_number(){
        return $this->secondary_title_two_number;
    }
    public function getsecondary_title_two(){
        return $this->secondary_title_two;
    }
    public function getsource_number(){
        return $this->source_number;
    }
    public function getcomments(){
        return $this->comments;
    }
    public function getexporterdata(){
        return $this->FIELDS['organiser'][app()->getLocale()];
    }
    public function getcreatedby(){
        return $this->FIELDS['author'][app()->getLocale()];

    }
    /**/

    /*public function language()
    {
        return $this->hasOne(Language::class);
    }*/
    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'sourcegroup_id');
    }

    const NAME = [
        'ar' => 'المؤتمرات الدولية',
        'en' => 'International conferences',
    ];
    const GROUPE_CODE = ['F'];

    public $FIELDS = [
        'organiser' => [
            'ar' => 'اسم الجهة المنظمة للمؤتمر',
            'en' => 'Conference Organiser',
        ],
        'conference_title' => [
            'ar' => 'عنوان المؤتمر',
            'en' => 'Conference title',
        ],
        'year' => [
            'ar' => 'سنة الانعقاد',
            'en' => 'Session year',
        ],
        'session_number' => [
            'ar' => 'رقم الجلسة',
            'en' => 'Session number',
        ],
        'session_title' => [
            'ar' => 'عنوان الجلسة',
            'en' => 'Session title',
        ],
        'research_number' => [
            'ar' => 'رقم البحث',
            'en' => 'Research number',
        ],
        'title' => [
            'ar' => 'عنوان البحث',
            'en' => 'Research title',
        ],
        'author' => [
            'ar' => 'اسم الباحث',
            'en' => 'Researcher name',
        ],
        'chapter_number' => [
            'ar' => 'رقم الفصل',
            'en' => 'Chapter number',
        ],
        'chapter_title' => [
            'ar' => 'عنوان الفصل',
            'en' => 'Chapter title',
        ],
        'search_number' => [
            'ar' => 'رقم المبحث',
            'en' => 'Search number',
        ],
        'search_title' => [
            'ar' => 'عنوان المبحث',
            'en' => 'Search title',
        ],
        'request_number' => [
            'ar' => 'رقم المطلب',
            'en' => 'Request number',
        ],
        'request_title' => [
            'ar' => 'عنوان المطلب',
            'en' => 'Request title',
        ],
        'secondary_title_one_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_one' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'secondary_title_two_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_two' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'page_number' => [
            'ar' => 'رقم الصفحة',
            'en' => 'Page number',
        ],
        'source_number' => [
            'ar' => 'ترقيم المصدر',
            'en' => 'Source numbering',
        ],
        'LANGUAGE' => [
            'ar' => 'اللغة',
            'en' => 'LANGUAGE',
        ],
        'comments' => [
            'ar' => 'ملاحظات',
            'en' => 'Notes',
        ],
    ];

    public function getDataOderDetails(){

        $fields = $this->FIELDS;

        if(isset($fields['LANGUAGE']))
        unset($fields['LANGUAGE']);
        if(isset($fields['comments']))
        unset($fields['comments']);
        if(isset($fields['source_number']))
        unset($fields['source_number']);

        $output = [];

        foreach($fields as $t => $v) {
            $output[$t] = [
                'label' => $this->FIELDS[$t][app()->getLocale()],
                'data' => $this->{$t},
            ];

        }
        return $output;
    }

    public function getDataOderResults(){
        return [
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],
            'conference_title' => [
                'label' => $this->FIELDS['conference_title'][app()->getLocale()],
                'data' => $this->conference_title,
            ],
            'organiser' => [
                'label' => $this->FIELDS['organiser'][app()->getLocale()],
                'data' => $this->organiser,
            ],
            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ],
        ];
    }
}
