<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;
use <PERSON>tie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\SoftDeletes;


class Regulation extends Model
{
    use HasFactory;
    use Searchable;
    use HasSlug;
    use SoftDeletes;
            /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }
    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected $fillable = [
        'code',
        'sourcegroup_id',
        'country','slug',
        'regulation_number',
        'regulation_title',
        'author',
        'type',
        'year',
        'serial_number',
        'title',
        'act_number',
        'act_title',
        'chapter_number',
        'chapter_title',
        'section_number',
        'section_title',
        'secondary_title_one_number',
        'secondary_title_one',
        'secondary_title_two_number',
        'secondary_title_two',
        'page_number',
        'source_number',
        'language_id',
        'comments','created_at','updated_at',
        'is_valid'

    ];

    protected $nullable = ['sourcegroup_id'];

    public function getClassname(){
        return 'Regulation';
    }
    public function getTitle(){
        return $this->title;
    }
    public function getType(){
        return '';
    }
    public function article_second_title(){
        return 'regulation_title';
    }
    public function article_content_second_title(){
        return $this->regulation_title;
    }
    public function article_content_extend_title(){
        return $this->secondary_title_one;
    }
    public function article_third_title(){
        return $this->author;
    }
    public function article_content_third_title(){
        return $this->year;
    }
    public function getexporter(){
        return $this->country;
    }

    /**/
    public function getcode(){
        return $this->code;
    }
    public function getauthor(){
        return $this->author;
    }
    public function getyear(){
        return $this->year;
    }
    public function getfolder_number(){
        return $this->folder_number;
    }
    public function getact_number(){
        return $this->act_number;
    }
    public function getserial_number(){
        return $this->serial_number;
    }
    public function getmain_title_number(){
        return '';
    }
    public function getact_title(){
        return $this->act_title;
    }
    public function getchapter_number(){
        return $this->chapter_number;
    }
    public function getchapter_title(){
        return $this->chapter_title;
    }
    public function getsearch_number(){
        return $this->search_number;
    }
    public function getsearch_title(){
        return $this->search_title;
    }
    public function getrequest_number(){
        return $this->request_number;
    }
    public function getrequest_title(){
        return $this->request_title;
    }
    public function getsecondary_title_one_number(){
        return $this->secondary_title_one_number;
    }
    public function getsecondary_title_one(){
        return $this->secondary_title_one;
    }
    public function getsecondary_title_two_number(){
        return $this->secondary_title_two_number;
    }
    public function getsecondary_title_two(){
        return $this->secondary_title_two;
    }
    public function getsource_number(){
        return $this->source_number;
    }
    public function getcomments(){
        return $this->comments;
    }
    public function getexporterdata(){
        return $this->FIELDS['country'][app()->getLocale()];
    }
    public function getcreatedby(){
        return $this->FIELDS['author'][app()->getLocale()];

    }
    /**/

    /*public function language()
    {
        return $this->hasOne(Language::class);
    }*/
    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'sourcegroup_id');
    }

    const NAME = [
        'ar' => 'القوانين و التشريعات',
        'en' => 'Rules and regulations',
    ];
    const GROUPE_CODE = ['K'];
    public $FIELDS = [
        'country' => [
            'ar' => 'الدولة المصدرة',
            'en' => 'Issuing country',
        ],
        'regulation_number' => [
            'ar' => 'رقم القانون أو التشريع',
            'en' => 'Rule or Regulation number',
        ],
        'regulation_title' => [
            'ar' => 'عنوان القانون أو التشريع',
            'en' => 'Rule or Regulation Title',
        ],
        'author' => [
            'ar' => 'الجهة المصدرة',
            'en' => 'Issuer',
        ],
        'type' => [
            'ar' => 'النوع',
            'en' => 'Type',
        ],
        'year' => [
            'ar' => 'سنة الإصدار',
            'en' => 'Year of release',
        ],
        'serial_number' => [
            'ar' => 'رقم العنوان',
            'en' => 'Title number',
        ],
        'title' => [
            'ar' => 'العنوان',
            'en' => 'Title',
        ],
        'act_number' => [
            'ar' => 'رقم الباب',
            'en' => 'Act number',
        ],
        'act_title' => [
            'ar' => 'الباب',
            'en' => 'Act',
        ],
        'chapter_number' => [
            'ar' => 'رقم الفصل',
            'en' => 'Chapter number',
        ],
        'chapter_title' => [
            'ar' => 'الفصل',
            'en' => 'Chapter',
        ],
        'section_number' => [
            'ar' => 'رقم القسم',
            'en' => 'Section number',
        ],
        'section_title' => [
            'ar' => 'القسم',
            'en' => 'Section',
        ],
        'secondary_title_one_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_one' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'secondary_title_two_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_two' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'page_number' => [
            'ar' => 'رقم الصفحة',
            'en' => 'Page number',
        ],
        'source_number' => [
            'ar' => 'ترقيم المصدر',
            'en' => 'Source numbering',
        ],
        'LANGUAGE' => [
            'ar' => 'اللغة',
            'en' => 'LANGUAGE',
        ],
        'comments' => [
            'ar' => 'ملاحظات',
            'en' => 'Notes',
        ],
    ];

    public function getDataOderDetails(){
        
        $fields = $this->FIELDS;
        if(isset($fields['LANGUAGE']))
        unset($fields['LANGUAGE']);
        if(isset($fields['comments']))
        unset($fields['comments']);
        if(isset($fields['source_number']))
        unset($fields['source_number']);
        $output = [];

        foreach($fields as $t => $v) {
            $output[$t] = [
                'label' => $this->FIELDS[$t][app()->getLocale()],
                'data' => $this->{$t},
            ];

        }
        return $output;
    }

    public function getDataOderResults(){
        return [
            
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'regulation_title' => [
                'label' => $this->FIELDS['regulation_title'][app()->getLocale()],
                'data' => $this->regulation_title,
            ],
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],
            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ],
        ];
    }
}
