<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\SoftDeletes;


class Book extends Model
{
    use HasFactory;
    use Searchable;
    use HasSlug;
    use SoftDeletes;
            /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }
    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
    protected $fillable = [
    'code',
    'updated_at',
    'created_at',
    'sourcegroup_id',
    'title',
    'author',
    'year',
    'publisher',
    'folder_number',
    'act_number',
    'act_title',
    'chapter_number',
    'chapter_title',
    'search_number',
    'search_title',
    'request_number',
    'request_title',
    'secondary_title_one_number',
    'secondary_title_one',
    'secondary_title_two_number',
    'secondary_title_two',
    'page_number',
    'source_number',
    'language_id',
    'comments'
    ];

    public $table = 'books';



    public function getClassname(){
        return 'Book';
    }

    public function getTitle(){
        return $this->title;
    }
    public function getType(){
        return '';
    }
    public function article_second_title(){
        return 'author';
    }
    public function article_content_second_title(){
        return $this->author;
    }
    public function article_content_extend_title(){
        return $this->secondary_title_one;
    }
    public function article_third_title(){
        return $this->publisher;
    }
    public function article_content_third_title(){
        return $this->year;
    }
    public function getexporter(){
        return $this->publisher;
    }

    /**/
    public function getcode(){
        return $this->code;
    }
    public function getauthor(){
        return $this->author;
    }
    public function getyear(){
        return $this->year;
    }
    public function getserial_number(){
        return '';
    }
    public function getmain_title_number(){
        return '';
    }
    public function getfolder_number(){
        return $this->folder_number;
    }
    public function getact_number(){
        return $this->act_number;
    }
    public function getact_title(){
        return $this->act_title;
    }
    public function getchapter_number(){
        return $this->chapter_number;
    }
    public function getchapter_title(){
        return $this->chapter_title;
    }
    public function getpublisher(){
        return $this->publisher;
    }
    public function getsearch_number(){
        return $this->search_number;
    }
    public function getsearch_title(){
        return $this->search_title;
    }
    public function getrequest_number(){
        return $this->request_number;
    }
    public function getrequest_title(){
        return $this->request_title;
    }
    public function getsecondary_title_one_number(){
        return $this->secondary_title_one_number;
    }
    public function getsecondary_title_one(){
        return $this->secondary_title_one;
    }
    public function getsecondary_title_two_number(){
        return $this->secondary_title_two_number;
    }
    public function getsecondary_title_two(){
        return $this->secondary_title_two;
    }
    public function getsource_number(){
        return $this->source_number;
    }
    public function getcomments(){
        return $this->comments;
    }
    public function getexporterdata(){
        return $this->FIELDS['publisher'][app()->getLocale()];
    }
    public function getcreatedby(){
        return $this->FIELDS['author'][app()->getLocale()];

    }
    /**/

   /* public function language()
    {
        return $this->hasOne(Language::class);
    }*/
    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'sourcegroup_id');
    }

    const NAME = [
        'ar' => 'الكتب',
        'en' => 'Books',
    ];
    const GROUPE_CODE = ['G'];
    public $FIELDS = [
        'title' => [
            'ar' => 'عنوان الكتاب',
            'en' => 'Book title',
        ],
        'author' => [
            'ar' => 'اسم المؤلف',
            'en' => 'Author name',
        ],
        'year' => [
            'ar' => 'سنة الإصدار',
            'en' => 'Release year',
        ],
        'publisher' => [
            'ar' => 'الدار / الجهة المصدرة للكتاب',
            'en' => 'Publisher',
        ],
        'folder_number' => [
            'ar' => 'رقم المجلد',
            'en' => 'Folder number',
        ],
        'act_number' => [
            'ar' => 'رقم الباب',
            'en' => 'Act number',
        ],
        'act_title' => [
            'ar' => 'عنوان الباب',
            'en' => 'Act title',
        ],
        'chapter_number' => [
            'ar' => 'رقم الفصل',
            'en' => 'Chapter number',
        ],
        'chapter_title' => [
            'ar' => 'عنوان الفصل',
            'en' => 'Chapter title',
        ],
        'search_number' => [
            'ar' => 'رقم المبحث',
            'en' => 'Search number',
        ],
        'search_title' => [
            'ar' => 'عنوان المبحث',
            'en' => 'Search title',
        ],
        'request_number' => [
            'ar' => 'رقم المطلب',
            'en' => 'Request number',
        ],
        'request_title' => [
            'ar' => 'عنوان المطلب',
            'en' => 'Request title',
        ],
        'secondary_title_one_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_one' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'secondary_title_two_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_two' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'page_number' => [
            'ar' => 'رقم الصفحة',
            'en' => 'Page number',
        ],
        'source_number' => [
            'ar' => 'ترقيم المصدر',
            'en' => 'Source numbering',
        ],
        'LANGUAGE' => [
            'ar' => 'اللغة',
            'en' => 'LANGUAGE',
        ],
        'comments' => [
            'ar' => 'ملاحظات',
            'en' => 'Notes',
        ],
    ];

    public function getDataOderDetails(){
        return [
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],
            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ],
            'publisher' => [
                'label' => $this->FIELDS['publisher'][app()->getLocale()],
                'data' => $this->publisher,
            ],
            'folder_number' => [
                'label' => $this->FIELDS['folder_number'][app()->getLocale()],
                'data' => $this->folder_number,
            ],
            'act_number' => [
                'label' => $this->FIELDS['act_number'][app()->getLocale()],
                'data' => $this->act_number,
            ],
            'act_title' => [
                'label' => $this->FIELDS['act_title'][app()->getLocale()],
                'data' => $this->act_title,
            ],
            'chapter_number' => [
                'label' => $this->FIELDS['chapter_number'][app()->getLocale()],
                'data' => $this->chapter_number,
            ],
            'chapter_title' => [
                'label' => $this->FIELDS['chapter_title'][app()->getLocale()],
                'data' => $this->chapter_title,
            ],
            'search_number' => [
                'label' => $this->FIELDS['search_number'][app()->getLocale()],
                'data' => $this->search_number,
            ],
            'search_title' => [
                'label' => $this->FIELDS['search_title'][app()->getLocale()],
                'data' => $this->search_title,
            ],
            'request_number' => [
                'label' => $this->FIELDS['request_number'][app()->getLocale()],
                'data' => $this->request_number,
            ],
            'request_title' => [
                'label' => $this->FIELDS['request_title'][app()->getLocale()],
                'data' => $this->request_title,
            ],
            'secondary_title_one_number' => [
                'label' => $this->FIELDS['secondary_title_one_number'][app()->getLocale()],
                'data' => $this->secondary_title_one_number,
            ],
            'secondary_title_one' => [
                'label' => $this->FIELDS['secondary_title_one'][app()->getLocale()],
                'data' => $this->secondary_title_one,
            ],
            'secondary_title_two_number' => [
                'label' => $this->FIELDS['secondary_title_two_number'][app()->getLocale()],
                'data' => $this->secondary_title_two_number,
            ],
            'secondary_title_two' => [
                'label' => $this->FIELDS['secondary_title_two'][app()->getLocale()],
                'data' => $this->secondary_title_two,
            ],
            'page_number' => [
                'label' => $this->FIELDS['page_number'][app()->getLocale()],
                'data' => $this->page_number,
            ],
           /* 'source_number' => [
                'label' => $this->FIELDS['source_number'][app()->getLocale()],
                'data' => $this->source_number,
            ],

            'comments' => [
                'label' => $this->FIELDS['comments'][app()->getLocale()],
                'data' => $this->comments,
            ],*/
        ];
    }

    public function getDataOderResults(){
        return [
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],

            'publisher' => [
                'label' => $this->FIELDS['publisher'][app()->getLocale()],
                'data' => $this->publisher,
            ],
             'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ],
        ];
    }
}
