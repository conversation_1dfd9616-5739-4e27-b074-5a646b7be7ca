<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>\Scout\Searchable;
use <PERSON>tie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\SoftDeletes;

class LegalBody extends Model
{
    use HasFactory;
    use Searchable;
    use HasSlug;


    use SoftDeletes;
            /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }
    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected $fillable = [
        'code',
        'sourcegroup_id',
        'author',
        'year',
        'serial_number',
        'secondary_number_one',
        'secondary_number_two',
        'group_title',
        'title',
        'secondary_title_one_number',
        'secondary_title_one',
        'secondary_title_two_number',
        'secondary_title_two',
        'page_number',
        'source_number',
        'language_id',
        'comments',
        'created_at',
        'updated_at'

    ];
    // public function toSearchableArray()
    // {
    //     return [
    //         'serial_number'=>$this->serial_number,
    //         'secondary_number_one'=>$this->secondary_number_one,
    //         'secondary_number_two'=>$this->secondary_number_two,
    //         'group_title'=>$this->group_title,
    //         'title'=>$this->title,
    //         'secondary_title_one_number'=>$this->secondary_title_one_number,
    //         'secondary_title_one'=>$this->secondary_title_one,
    //         'secondary_title_two_number'=>$this->secondary_title_two_number,
    //         'secondary_title_two'=>$this->secondary_title_two,
    //         'page_number'=>$this->page_number,
    //         'source_number'=>$this->source_number,
    //     ];
    // }


    /*
    public function getSearchSettings()
    {
        return [
            'analysis' => [
                'filter' => [
                    'arabic_stop' => [
                        'type' => 'stop',
                        'stopwords' => '_arabic_',
                    ],
                    'arabic_keywords' => [
                        'type' => 'keyword_marker',
                        'keywords' => ['مثال'],
                    ],
                    'arabic_stemmer' => [
                        'type' => 'stemmer',
                        'language' => 'arabic',
                    ],
                ],
                'analyzer' => [
                    'rebuilt_arabic' => [
                        'tokenizer' => 'standard',
                        'filter' => [
                            'lowercase',
                            'decimal_digit',
                            'arabic_stop',
                            'arabic_normalization',
                            'arabic_keywords',
                            'arabic_stemmer',
                        ],
                    ],
                ],
            ],
        ];
    }*/
    public function getClassname(){
        return 'LegalBody';
    }

    public function getTitle(){
        return $this->title;
    }
    public function getType(){
        return '';
    }
    public function article_second_title(){
        return 'group_title';
    }
    public function getserial_number(){
        return $this->serial_number;
    }
    public function getmain_title_number(){
        return '';
    }
    public function article_content_second_title(){
        return $this->group_title;
    }
    public function getgroup_title(){
        return $this->group_title;
    }
    public function article_content_extend_title(){
        return $this->secondary_title_one;
    }
    public function article_third_title(){
        return $this->author;
    }
    public function article_content_third_title(){
        return $this->year;
    }
    public function getexporter(){
        return $this->author;
    }
    /**/
    public function getcode(){
        return $this->code;
    }
    public function getauthor(){
        return $this->author;
    }
    public function getyear(){
        return $this->year;
    }
    public function getfolder_number(){
        return $this->folder_number;
    }
    public function getact_number(){
        return $this->act_number;
    }
    public function getact_title(){
        return $this->act_title;
    }
    public function getchapter_number(){
        return $this->chapter_number;
    }
    public function getchapter_title(){
        return $this->chapter_title;
    }
    public function getsearch_number(){
        return $this->search_number;
    }
    public function getsearch_title(){
        return $this->search_title;
    }
    public function getrequest_number(){
        return $this->request_number;
    }
    public function getrequest_title(){
        return $this->request_title;
    }
    public function getsecondary_number_one(){
        return $this->secondary_number_one;
    }
    public function getsecondary_number_two(){
        return $this->secondary_number_two;
    }
    public function getsecondary_title_one_number(){
        return $this->secondary_title_one_number;
    }
    public function getsecondary_title_one(){
        return $this->secondary_title_one;
    }
    public function getsecondary_title_two_number(){
        return $this->secondary_title_two_number;
    }
    public function getsecondary_title_two(){
        return $this->secondary_title_two;
    }
    public function getsource_number(){
        return $this->source_number;
    }
    public function getcomments(){
        return $this->comments;
    }
    public function getexporterdata(){
        return $this->FIELDS['author'][app()->getLocale()];
    }
    public function getcreatedby(){
        return $this->FIELDS['author'][app()->getLocale()];

    }
    /**/

    /*public function language()
    {
        return $this->hasOne(Language::class);
    }*/
    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'sourcegroup_id');
    }

    const NAME = [
        'ar' => 'قرارات وفتاوى الهيئات الشرعية',
        'en' => 'Decisions and advisory opinions of legal bodies',
    ];
    const GROUPE_CODE = ['D'];

    public $FIELDS = [
        'author' => [
            'ar' => 'الجهة المصدرة',
            'en' => 'Issuer',
        ],
        'year' => [
            'ar' => 'سنة الإصدار',
            'en' => 'Release year',
        ],
        'serial_number' => [
            'ar' => 'الرقم التسلسلي',
            'en' => 'Serial number',
        ],
        'secondary_number_one' => [
            'ar' => 'الرقم الفرعي الأول',
            'en' => 'Secondary number',
        ],
        'secondary_number_two' => [
            'ar' => 'الرقم الفرعي الثاني',
            'en' => 'Secondary number',
        ],
        'group_title' => [
            'ar' => 'عنوان المجموعة',
            'en' => 'Group title',
        ],
        'title' => [
            'ar' => 'عنوان الفتوى',
            'en' => 'advisory title',
        ],
        'secondary_title_one_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_one' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'secondary_title_two_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_two' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'page_number' => [
            'ar' => 'رقم الصفحة',
            'en' => 'Page number',
        ],
        'source_number' => [
            'ar' => 'ترقيم المصدر',
            'en' => 'Source numbering',
        ],
        'LANGUAGE' => [
            'ar' => 'اللغة',
            'en' => 'LANGUAGE',
        ],
        'comments' => [
            'ar' => 'ملاحظات',
            'en' => 'Notes',
        ],
    ];

    public function getDataOderDetails(){

        $fields = $this->FIELDS;
        if(isset($fields['LANGUAGE']))
        unset($fields['LANGUAGE']);
        if(isset($fields['comments']))
        unset($fields['comments']);
        if(isset($fields['source_number']))
        unset($fields['source_number']);
        $output = [];

        foreach($fields as $t => $v) {
            $output[$t] = [
                'label' => $this->FIELDS[$t][app()->getLocale()],
                'data' => $this->{$t},
            ];

        }
        return $output;
    }

    public function getDataOderResults(){
        return [
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'group_title' => [
                'label' => $this->FIELDS['group_title'][app()->getLocale()],
                'data' => $this->group_title,
            ],

            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],

            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ]
        ];
    }
}
