<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\SoftDeletes;


class Report extends Model
{
    use HasFactory;
    use Searchable;
    use HasSlug;
    use SoftDeletes;
            /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }
    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    protected $fillable = [
        'code',
        'slug',
        'sourcegroup_id',
        'author',
        'serial_number',
        'title',
        'year',
        'act_number',
        'act_title',
        'chapter_number',
        'chapter_title',
        'section_number',
        'section_title',
        'secondary_title_one_number',
        'secondary_title_one',
        'secondary_title_two_number',
        'secondary_title_two',
        'page_number',
        'source_number',
        'language_id',
        'comments','created_at','updated_at'
    ];

    public function getClassname(){
        return 'Report';
    }
    public function getTitle(){
        return $this->title;
    }
    public function getType(){
        return '';
    }
    public function article_second_title(){
        return 'author';
    }
    public function article_content_second_title(){
        return $this->author;
    }
    public function article_content_extend_title(){
        return $this->secondary_title_one;
    }
    public function article_third_title(){
        return 'year';
    }
    public function article_content_third_title(){
        return $this->year;
    }
    public function getexporter(){
        return $this->author;
    }

    /**/
    public function getcode(){
        return $this->code;
    }
    public function getauthor(){
        return $this->author;
    }
    public function getyear(){
        return $this->year;
    }
    public function getfolder_number(){
        return $this->folder_number;
    }
    public function getact_number(){
        return $this->act_number;
    }
    public function getserial_number(){
        return $this->serial_number;
    }
    public function getmain_title_number(){
        return '';
    }
    public function getact_title(){
        return $this->act_title;
    }
    public function getchapter_number(){
        return $this->chapter_number;
    }
    public function getchapter_title(){
        return $this->chapter_title;
    }
    public function getsearch_number(){
        return $this->search_number;
    }
    public function getsearch_title(){
        return $this->search_title;
    }
    public function getrequest_number(){
        return $this->request_number;
    }
    public function getrequest_title(){
        return $this->request_title;
    }
    public function getsecondary_title_one_number(){
        return $this->secondary_title_one_number;
    }
    public function getsecondary_title_one(){
        return $this->secondary_title_one;
    }
    public function getsecondary_title_two_number(){
        return $this->secondary_title_two_number;
    }
    public function getsecondary_title_two(){
        return $this->secondary_title_two;
    }
    public function getsource_number(){
        return $this->source_number;
    }
    public function getcomments(){
        return $this->comments;
    }
    public function getexporterdata(){
        return $this->FIELDS['author'][app()->getLocale()];
    }
    public function getcreatedby(){
        return $this->FIELDS['author'][app()->getLocale()];

    }

    /**/


    /*public function language()
    {
        return $this->hasOne(Language::class);
    }*/
    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'sourcegroup_id');
    }

    const NAME = [
        'ar' => 'التقارير المتعلقة بالصناعة المالية الإسلامية',
        'en' => 'Reports related to the Islamic financial industry',
    ];
    const GROUPE_CODE = ['L'];

    public $FIELDS = [
        'author' => [
            'ar' => 'الجهة المصدرة للتقرير',
            'en' => 'Issuer of the report',
        ],
        'serial_number' => [
            'ar' => 'الرقم الدوري للتقرير',
            'en' => 'Report serial number',
        ],
        'title' => [
            'ar' => 'عنوان التقرير',
            'en' => 'Report title',
        ],
        'year' => [
            'ar' => 'سنة الإصدار',
            'en' => 'Year of release',
        ],
        'act_number' => [
            'ar' => 'رقم الباب',
            'en' => 'Act number',
        ],
        'act_title' => [
            'ar' => 'الباب',
            'en' => 'Act',
        ],
        'chapter_number' => [
            'ar' => 'رقم الفصل',
            'en' => 'Chapter number',
        ],
        'chapter_title' => [
            'ar' => 'الفصل',
            'en' => 'Chapter',
        ],
        'section_number' => [
            'ar' => 'رقم القسم',
            'en' => 'Section number',
        ],
        'section_title' => [
            'ar' => 'القسم',
            'en' => 'Section',
        ],
        'secondary_title_one_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_one' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'secondary_title_two_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_two' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'page_number' => [
            'ar' => 'رقم الصفحة',
            'en' => 'Page number',
        ],
        'source_number' => [
            'ar' => 'ترقيم المصدر',
            'en' => 'Source numbering',
        ],
        'LANGUAGE' => [
            'ar' => 'اللغة',
            'en' => 'LANGUAGE',
        ],
        'comments' => [
            'ar' => 'ملاحظات',
            'en' => 'Notes',
        ],
    ];

    public function getDataOderDetails(){
        
        $fields = $this->FIELDS;
        if(isset($fields['LANGUAGE']))
        unset($fields['LANGUAGE']);
        if(isset($fields['comments']))
        unset($fields['comments']);
        if(isset($fields['source_number']))
        unset($fields['source_number']);
        $output = [];

        foreach($fields as $t => $v) {
            $output[$t] = [
                'label' => $this->FIELDS[$t][app()->getLocale()],
                'data' => $this->{$t},
            ];

        }
        return $output;
    }
    public function getDataOderResults(){
        return [
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ],
        ];
    }
}
