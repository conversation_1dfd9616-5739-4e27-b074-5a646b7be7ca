<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Nova\Fields\HasMany;

class Indice extends Model
{
    use HasFactory;
    protected $table = 'indices';

    protected $fillable =   [
        'class',
        'content',
        'status',
        'type',
        'sort_order',
    ];

    public function indicedetails(){
        return $this->HasMany(Indicedetail::class);
    }
}
