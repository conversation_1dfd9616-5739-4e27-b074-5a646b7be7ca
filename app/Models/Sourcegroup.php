<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sourcegroup extends Model
{
    use HasFactory;
    
    protected $fillable = [        
        'groupname',
        'groupnumber',
        'sourcetitle',
        'sourcetitlenumber'
    ];

    public function participers(){
        return $this->hasMany(Participer::class,'participatble_id');
    }
    public function magazines(){
        return $this->hasMany(Magazine::class,'sourcegroup_id');
    }
    public function interconferences(){
        return $this->hasMany(InterConference::class,'sourcegroup_id');
    }
    public function annualinterconferences(){
        return $this->hasMany(AnnualInterConference::class,'sourcegroup_id');
    }
    public function interstandards(){
        return $this->hasMany(InterStandard::class,'sourcegroup_id');
    }
    public function universitynewsletters(){
        return $this->hasMany(UniversityNewsletter::class,'sourcegroup_id');
    }
    public function jurisprudences(){
        return $this->hasMany(Jurisprudence::class,'sourcegroup_id');
    }
    public function interconferencesrecommendations(){
        return $this->hasMany(InterConferenceRecommendation::class,'sourcegroup_id');
    }
    public function legalbodies(){
        return $this->hasMany(LegalBody::class,'sourcegroup_id');
    }
    public function books(){
        return $this->hasMany(Book::class,'sourcegroup_id');
    }
    public function researches(){
        return $this->hasMany(Research::class,'sourcegroup_id');
    }
    public function regulations(){
        return $this->hasMany(Regulation::class,'sourcegroup_id');
    }
    public function reports(){
        return $this->hasMany(Report::class,'sourcegroup_id');
    }

}
