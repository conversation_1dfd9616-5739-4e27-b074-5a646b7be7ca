<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
class News extends Model
{
    use HasSlug;
    use HasFactory;

    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'status',
        'published_until',
        // 'additional_images',
    ];

    protected $casts = [
        'published_until' => 'datetime',
        // 'additional_images' => 'array',
    ];

    // Remove default attributes - let the cast handle empty values


    public function getRouteKeyName()
    {
        return 'slug';
    }
}
