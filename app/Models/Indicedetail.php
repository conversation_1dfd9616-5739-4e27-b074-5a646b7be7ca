<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class Indicedetail extends Model
{
    use HasFactory;

    use SortableTrait;

    public $sortable = [
      'order_column_name' => 'number',
      'sort_when_creating' => true,
      'ignore_policies' => true,
      'sort_on_has_many' => true,

    ];

    protected $primaryKey = 'id';

    protected $fillable =  [
        'indice_id',
        'number',
        'title1',
        'title2',
    ];

    public function indices()
    {
        return $this->belongsTo(Indice::class);
    }


}
