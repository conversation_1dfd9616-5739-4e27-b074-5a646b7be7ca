<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Participer extends Model
{
    use HasFactory;

    protected $fillable = [
        'labelrole',
        'participername',
        'countlines',
        'participerdate',
        'comment',
        'participatble_id'
    ];

    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'participatble_id');
    }

    
}
