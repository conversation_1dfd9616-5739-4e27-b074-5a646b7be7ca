<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use <PERSON>vel\Scout\Searchable;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\SoftDeletes;



class InterStandard extends Model
{
    use HasFactory;
    use Searchable;
    use HasSlug;
    use SoftDeletes;
            /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }
    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    // public function toSearchableArray()
    // {
    //     return [

    //         'code'=> $this->code,
    //         'author'=> $this->author,
    //         'serial_number'=> $this->serial_number,
    //         'title'=> $this->title,
    //         'year'=> $this->year,
    //         'main_title_number'=> $this->main_title_number,
    //         'main_title'=> $this->main_title,
    //         'secondary_title_one_number'=> $this->secondary_title_one_number,
    //         'secondary_title_one'=> $this->secondary_title_one,
    //         'secondary_title_two_number'=> $this->secondary_title_two_number,
    //         'secondary_title_two'=> $this->secondary_title_two,
    //         'page_number'=> $this->page_number,
    //         'source_number'=> $this->source_number,
    //     ];
    // }
    public function getElasticsearchMapping()
    {
        return [
            'properties' => [
                'code' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'author' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'serial_number' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'title' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'year' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'main_title_number' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'main_title' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'secondary_title_one_number' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'secondary_title_one' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'secondary_title_two_number' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'secondary_title_two' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'page_number' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'source_number' => [
                    'type' => 'text',
                    'index' => 'true', 
                ],
                'type' => [
                    'type' => 'text',
                    'index' => 'false', // Field is not searchable.
                ],
            ],
        ];
    }

    protected $fillable = [
        'code',
        'sourcegroup_id',
        'author',
        // 'type',
        'serial_number',
        'title',
        'year',
        'main_title_number',
        'main_title',
        'secondary_title_one_number',
        'secondary_title_one',
        'secondary_title_two_number',
        'secondary_title_two',
        'page_number',
        'source_number',
        'language_id',
        'comments','created_at','updated_at'
    ];

    public function getClassname(){
        return 'InterStandard';
    }
    public function getTitle(){
        return $this->title;
    }
    public function getType(){
        return $this->type;
    }
    public function article_second_title(){
        return 'author';
    }
    public function article_content_second_title(){
        return $this->author;
    }
    public function article_content_extend_title(){
        return $this->secondary_title_one;
    }
    public function article_third_title(){
        return $this->type;
    }
    public function article_content_third_title(){
        return $this->year;
    }
    public function getexporter(){
        return $this->author;
    }


    /**/
    public function getcode(){
        return $this->code;
    }
    public function getauthor(){
        return $this->author;
    }
    public function getyear(){
        return $this->year;
    }
    public function getfolder_number(){
        return $this->folder_number;
    }
    public function getact_number(){
        return $this->act_number;
    }
    public function getact_title(){
        return $this->act_title;
    }
    public function getserial_number(){
        return $this->serial_number;
    }
    public function getmain_title_number(){
        return $this->main_title_number;
    }
    public function getmain_title(){
        return $this->main_title;
    }
    public function getchapter_number(){
        return $this->chapter_number;
    }
    public function getchapter_title(){
        return $this->chapter_title;
    }
    public function getsearch_number(){
        return $this->search_number;
    }
    public function getsearch_title(){
        return $this->search_title;
    }
    public function getrequest_number(){
        return $this->request_number;
    }
    public function getrequest_title(){
        return $this->request_title;
    }
    public function getsecondary_title_one_number(){
        return $this->secondary_title_one_number;
    }
    public function getsecondary_title_one(){
        return $this->secondary_title_one;
    }
    public function getsecondary_title_two_number(){
        return $this->secondary_title_two_number;
    }
    public function getsecondary_title_two(){
        return $this->secondary_title_two;
    }
    public function getsource_number(){
        return $this->source_number;
    }
    public function getcomments(){
        return $this->comments;
    }
    public function getexporterdata(){
        return $this->FIELDS['author'][app()->getLocale()];
    }
    public function getcreatedby(){
        return $this->FIELDS['author'][app()->getLocale()];

    }
    /**/

    public function getlabels(){
        $labels['getexporter']="labels.".$this->getClassname().".getexporter";
        return $this->FIELDS;
        return $labels['author']="اسم الجهةاملصدرة للمعيار";
    }

    /*public function language()
    {
        return $this->hasOne(Language::class);
    }*/
    public function sourcegroups(){
        return $this->belongsTo(Sourcegroup::class,'sourcegroup_id');
    }

    const NAME = [
        'ar' => 'المعايير الدولية',
        'en' => 'International standards',
    ];
    const GROUPE_CODE = ['A'];
    public $FIELDS = [
        'author' => [
            'ar' => 'اسم الجهة المصدرة للمعيار',
            'en' => 'Issuer of the standard',
        ],
        'type' => [
            'ar' => 'نوع المعيار',
            'en' => 'Type',
        ],
        'serial_number' => [
            'ar' => 'الرقم التسلسلي للمعيار',
            'en' => 'Serial number',
        ],
        'title' => [
            'ar' => 'عنوان المعيار',
            'en' => 'Title',
        ],
        'year' => [
            'ar' => 'تاريخ الإصدار',
            'en' => 'Release date',
        ],
        'main_title_number' => [
            'ar' => 'رقم العنوان الرئيسي',
            'en' => 'Main title number',
        ],
        'main_title' => [
            'ar' => 'العنوان الرئيسي',
            'en' => 'Main title',
        ],
        'secondary_title_one_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_one' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'secondary_title_two_number' => [
            'ar' => 'رقم العنوان الفرعي',
            'en' => 'Secondary title number',
        ],
        'secondary_title_two' => [
            'ar' => 'العنوان الفرعي',
            'en' => 'Secondary title',
        ],
        'page_number' => [
            'ar' => 'رقم الصفحة',
            'en' => 'Page number',
        ],
        'source_number' => [
            'ar' => 'ترقيم المصدر',
            'en' => 'Source numbering',
        ],
        'LANGUAGE' => [
            'ar' => 'اللغة',
            'en' => 'LANGUAGE',
        ],
        'comments' => [
            'ar' => 'ملاحظات',
            'en' => 'Notes',
        ],
    ];

    public function getDataOderDetails(){
        return [
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->getauthor(),
            ],
            'type' => [
                'label' => $this->FIELDS['type'][app()->getLocale()],
                'data' => $this->getType(),
            ],
            'serial_number' => [
                'label' => $this->FIELDS['serial_number'][app()->getLocale()],
                'data' => $this->getserial_number(),
            ],
            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->getTitle(),
            ],
            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->getyear(),
            ],
            'main_title_number' => [
                'label' => $this->FIELDS['main_title_number'][app()->getLocale()],
                'data' => $this->getmain_title_number(),
            ],
            'main_title' => [
                'label' => $this->FIELDS['main_title'][app()->getLocale()],
                'data' => $this->getmain_title(),
            ],
            'secondary_title_one_number' => [
                'label' => $this->FIELDS['secondary_title_one_number'][app()->getLocale()],
                'data' => $this->getsecondary_title_one_number(),
            ],
            'secondary_title_one' => [
                'label' => $this->FIELDS['secondary_title_one'][app()->getLocale()],
                'data' => $this->getsecondary_title_one(),
            ],
            'secondary_title_two_number' => [
                'label' => $this->FIELDS['secondary_title_two_number'][app()->getLocale()],
                'data' => $this->getsecondary_title_two_number(),
            ],
            'secondary_title_two' => [
                'label' => $this->FIELDS['secondary_title_two'][app()->getLocale()],
                'data' => $this->getsecondary_title_two(),
            ],
            'page_number' => [
                'label' => $this->FIELDS['page_number'][app()->getLocale()],
                'data' => $this->page_number,
            ],
           /* 'source_number' => [
                'label' => $this->FIELDS['source_number'][app()->getLocale()],
                'data' => $this->source_number,
            ],

            'comments' => [
                'label' => $this->FIELDS['comments'][app()->getLocale()],
                'data' => $this->comments,
            ],*/
        ];
    }

    public function getDataOderResults(){
        return [

            'title' => [
                'label' => $this->FIELDS['title'][app()->getLocale()],
                'data' => $this->title,
            ],
            'author' => [
                'label' => $this->FIELDS['author'][app()->getLocale()],
                'data' => $this->author,
            ],
            'type' => [
                'label' => $this->FIELDS['type'][app()->getLocale()],
                'data' => $this->type,
            ],
            'year' => [
                'label' => $this->FIELDS['year'][app()->getLocale()],
                'data' => $this->year,
            ],
        ];
    }
}
