<?php

if (!function_exists('normalizeArabic')) {
    function normalizeArabic($text)
    {
        $normalizedText = Normalizer::normalize($text, Normalizer::FORM_C);
        $transformedText = str_replace(['أ', 'إ', 'آ'], 'ا', $normalizedText);
        return $transformedText;
    }
}

if (!function_exists('highlightTextArabic')) {
    function highlightTextArabic($search, $text, $label = null)
    {
        $shortVowels = array('َ', 'ُ', 'ِ', 'ْ', 'ّ');
        $stopWords = array("على", "في", "من", "إلى", "عن");
        if ($search) {
            $wordstoreplace = explode(' ', $search);


            $textItems = explode(' ', $text);
            // dump($text);
            if ($textItems && $label != "نوع المعيار") {
                foreach ($textItems as $k => $t) {
                    if (strlen($t) > 4) {
                        $buffer = $t;
                        foreach ($wordstoreplace as $word) {
                            if (!in_array($t, $stopWords) || str_contains($text, $search)) {
                                if (strlen($word) > 4) {
                                    if (
                                        str_contains(
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $word)),
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $t)
                                        ) || str_contains(
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $word),
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $t)
                                        )
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    if (
                                        str_contains(
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $word)) . 'ة',
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $t)
                                        ) || str_contains(
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $word),
                                            str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $t)
                                        )
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    if (str_contains(str_replace(['الا', 'الأ', 'الا', 'الآ', 'الا', 'الإ'], '', $t), str_replace($shortVowels, '', $word))) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    $word1 = str_replace('الا', 'الأ', $word);
                                    $word2 = str_replace('الا', 'الآ', $word);
                                    $word3 = str_replace('الا', 'الإ', $word);

                                    $word5 = str_replace(['الأ', 'الآ', 'الإ'], 'الا', $word);

                                    $word4 = ucfirst($word);

                                    if (
                                        str_contains($word1, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $buffer)))
                                        || str_contains($word1, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $buffer))
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    if (
                                        str_contains($word2, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $buffer)))
                                        || str_contains($word2, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $buffer))
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    if (
                                        str_contains($word3, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $buffer)))
                                        || str_contains($word3, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $buffer))
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    if (
                                        str_contains($word4, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $buffer)))
                                        || str_contains($word4, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $buffer))
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }

                                    if (
                                        str_contains($word5, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace($shortVowels, '', $buffer)))
                                        || str_contains($word5, str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $buffer))
                                    ) {
                                        $textItems[$k] = '<b class="matchsearchword">' . $t . '</b>';
                                    }
                                }
                            }
                        }
                    }
                }
                $text = implode(' ', $textItems);
            }
        }
        return $text;
    }
}

function generateCombinations($inputString, $currentIndex, $currentCombination, &$result)
{
    $synonymLetters = array(
        // 'أ' => array('ا', 'إ', 'آ', 'أ'),
        'ا' => array('ا', 'إ', 'آ', 'أ'),
        'ال' => array('ال'),
        // 'إ' => array('ا', 'إ', 'آ', 'أ'),
        // 'آ' => array('ا', 'إ', 'آ', 'أ'),
        // 'پ' => array('ب','پ'),
        // 'چ' => array('ج', 'چ'),
        // 'ک' => array('ك', 'ک'),
        // 'ى' => array('ي', 'ى')
    );

    if ($currentIndex === mb_strlen($inputString)) {
        $result[] = $currentCombination;
    } else {
        $currentChar = mb_substr($inputString, $currentIndex, 1);
        $currentChar2 = mb_substr($inputString, $currentIndex, 2);
        $synonyms = (isset($synonymLetters[$currentChar]) && !isset($synonymLetters[$currentChar2])) ? $synonymLetters[$currentChar] : array($currentChar);

        foreach ($synonyms as $synonym) {
            $updatedCombination = $currentCombination . $synonym;
            generateCombinations($inputString, $currentIndex + 1, $updatedCombination, $result);
        }
    }
}
if (!function_exists('regenerateStemmerSearch')) {
    function regenerateStemmerSearch($search)
    {
        return $search;
    }
}
