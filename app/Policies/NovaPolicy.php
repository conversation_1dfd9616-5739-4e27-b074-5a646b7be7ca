<?php

namespace App\Policies;

use App\Models\User;
use App\Services\AuthorizationService;
use Illuminate\Auth\Access\HandlesAuthorization;

class NovaPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view Nova dashboard.
     *
     * @param  \App\Models\User  $user
     * @return bool
     */
    public function viewNova(User $user): bool
    {
        return AuthorizationService::canAccessNova();
    }

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @param  string  $resource
     * @return bool
     */
    public function viewAny(User $user, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'index');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function view(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'view', $model);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @param  string  $resource
     * @return bool
     */
    public function create(User $user, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'create');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function update(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'update', $model);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function delete(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'delete', $model);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function restore(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'restore', $model);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function forceDelete(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'forceDelete', $model);
    }

    /**
     * Determine whether the user can replicate the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function replicate(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'replicate', $model);
    }

    /**
     * Determine whether the user can attach models.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function attachAny(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'update', $model);
    }

    /**
     * Determine whether the user can attach the model.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  mixed  $target
     * @param  string  $resource
     * @return bool
     */
    public function attach(User $user, $model, $target, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'update', $model);
    }

    /**
     * Determine whether the user can detach models.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  mixed  $target
     * @param  string  $resource
     * @return bool
     */
    public function detach(User $user, $model, $target, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'update', $model);
    }

    /**
     * Determine whether the user can detach any models.
     *
     * @param  \App\Models\User  $user
     * @param  mixed  $model
     * @param  string  $resource
     * @return bool
     */
    public function detachAny(User $user, $model, string $resource): bool
    {
        return AuthorizationService::canAccess($resource, 'update', $model);
    }
}
