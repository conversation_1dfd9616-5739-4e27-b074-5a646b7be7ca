<?php

namespace App\Mail\Transport;

use App\Services\MicrosoftGraphMailService;
use Symfony\Component\Mailer\Transport\AbstractTransport;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Envelope;

class MicrosoftGraphTransport extends AbstractTransport
{
    protected $microsoftGraphService;

    public function __construct(MicrosoftGraphMailService $microsoftGraphService)
    {
        $this->microsoftGraphService = $microsoftGraphService;
        parent::__construct();
    }

    /**
     * Send the given message.
     *
     * @param \Symfony\Component\Mailer\SentMessage $message
     * @return void
     */
    protected function doSend(SentMessage $message): void
    {
        $originalMessage = $message->getOriginalMessage();
        $emailData = $this->convertMessageToEmailData($originalMessage);

        $success = $this->microsoftGraphService->sendEmail($emailData);

        if (!$success) {
            throw new \Exception('Failed to send email via Microsoft Graph');
        }
    }

    /**
     * Convert Symfony Email message to array format for Microsoft Graph
     *
     * @param \Symfony\Component\Mime\Email $message
     * @return array
     */
    protected function convertMessageToEmailData($message): array
    {
        $emailData = [
            'subject' => $message->getSubject(),
            'body' => $this->getMessageBody($message),
            'body_type' => $this->getBodyType($message),
            'to' => $this->extractAddresses($message->getTo()),
            'cc' => $this->extractAddresses($message->getCc()),
            'bcc' => $this->extractAddresses($message->getBcc()),
            'reply_to' => $this->extractAddresses($message->getReplyTo()),
            'attachments' => $this->extractAttachments($message),
        ];

        // Set from address if specified
        $fromAddresses = $message->getFrom();
        if (!empty($fromAddresses)) {
            $fromAddress = reset($fromAddresses); // Get the first Address object
            $emailData['from'] = $fromAddress->getAddress();
        }

        return $emailData;
    }

    /**
     * Get message body content
     *
     * @param \Symfony\Component\Mime\Email $message
     * @return string
     */
    protected function getMessageBody(Email $message): string
    {
        // Try to get HTML body first, then fall back to text
        if ($message->getHtmlBody()) {
            return $message->getHtmlBody();
        }
        
        return $message->getTextBody() ?? '';
    }

    /**
     * Determine body content type
     *
     * @param \Symfony\Component\Mime\Email $message
     * @return string
     */
    protected function getBodyType(Email $message): string
    {
        return $message->getHtmlBody() ? 'HTML' : 'Text';
    }

    /**
     * Extract email addresses from address collection
     *
     * @param \Symfony\Component\Mime\Address[] $addresses
     * @return array
     */
    protected function extractAddresses(array $addresses): array
    {
        $extracted = [];
        
        foreach ($addresses as $address) {
            $extracted[] = [
                'email' => $address->getAddress(),
                'name' => $address->getName()
            ];
        }

        return $extracted;
    }

    /**
     * Extract attachments from message
     *
     * @param \Symfony\Component\Mime\Email $message
     * @return array
     */
    protected function extractAttachments(Email $message): array
    {
        $attachments = [];
        
        foreach ($message->getAttachments() as $attachment) {
            $attachments[] = [
                'name' => $attachment->getFilename() ?? 'attachment',
                'content' => $attachment->getBody(),
                'content_type' => $attachment->getContentType() ?? 'application/octet-stream'
            ];
        }

        return $attachments;
    }

    /**
     * Get the string representation of the transport.
     *
     * @return string
     */
    public function __toString(): string
    {
        return 'microsoft-graph';
    }
}
