<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ExampleMicrosoftGraphMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $content;

    /**
     * Create a new message instance.
     *
     * @param mixed $user
     * @param string $content
     * @return void
     */
    public function __construct($user, $content = '')
    {
        $this->user = $user;
        $this->content = $content;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Example Email via Microsoft Graph')
                    ->view('emails.example-microsoft-graph')
                    ->with([
                        'user' => $this->user,
                        'content' => $this->content,
                    ]);
    }
}
