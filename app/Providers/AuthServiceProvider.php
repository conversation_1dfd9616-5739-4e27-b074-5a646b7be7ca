<?php

namespace App\Providers;

use App\Models\Indicedetail;
use App\Models\User;
use App\Models\Contact;
use App\Models\News;
use App\Policies\IndicedetailPolicy;
use App\Policies\UserPolicy;
use App\Policies\ContactPolicy;
use App\Policies\NewsPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        //Book::class => BookPolicy::class,
        User::class => UserPolicy::class,
        Indicedetail::class => IndicedetailPolicy::class,
        Contact::class => ContactPolicy::class,
        News::class => NewsPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
