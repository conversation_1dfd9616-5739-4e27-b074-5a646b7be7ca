<?php

namespace App\Providers;

use App\Mail\Transport\MicrosoftGraphTransport;
use App\Services\MicrosoftGraphMailService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Mail;

class MicrosoftGraphMailServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Register the Microsoft Graph Mail Service
        $this->app->singleton(MicrosoftGraphMailService::class, function () {
            return new MicrosoftGraphMailService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Extend the Mail manager to add the Microsoft Graph transport
        Mail::extend('microsoft-graph', function (array $config) {
            $microsoftGraphService = $this->app->make(MicrosoftGraphMailService::class);
            return new MicrosoftGraphTransport($microsoftGraphService);
        });
    }
}
