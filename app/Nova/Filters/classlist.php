<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;

class classlist extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->where('class','=',$value);
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'A'=>'A',
            'B'=>'B',
            'C'=>'C',
            'D'=>'D',
            'E'=>'E',
            'F'=>'F',
            'G'=>'G',
            'H'=>'H',
            'I'=>'I',
            'J'=>'J',
            'K'=>'K',
            'L'=>'L',
        ];
    }
}
