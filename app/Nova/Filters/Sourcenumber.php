<?php

namespace App\Nova\Filters;

use App\Models\Book;
use App\Models\Modelgroup;
use Illuminate\Http\Request;
use Laravel\Nova\Filters\Filter;

class Sourcenumber extends Filter
{

    public $component = 'select-filter';


    public function apply(Request $request, $query, $value)
    {
        return $query->where('source_number', $value)->orwhere('source_number', 'LIKE', '%'.$value.'%');
    }


    public function options(Request $request)
    {
        $tablename = str_replace('-','_',$request->segment(2));

        $tablenames= [

            'annual_inter_conferences'=>'AnnualInterConference',
            'books'=>'Book',
            'inter_conferences'=>'InterConference',
            'inter_conference_recommendations'=>'InterConferenceRecommendation',
            'inter_standards'=>'InterStandard',
            'jurisprudences'=>'Jurisprudence',
            'legal_bodies'=>'LegalBody',
            'magazines'=>'Magazine',
            'regulations'=>'Regulation',
            'reports'=>'Report',
            'research'=>'Research',
            'university_newsletters'=>'UniversityNewsletter',
        ];
        $model =  '\App\Models\\'.$tablenames[$tablename];

        if($model=="\App\Models\Jurisprudence")
            $selectedattribute ='source_number_two';
        else
            $selectedattribute ='source_number';
        $items = $model::select($selectedattribute)->distinct()->get();

        $arraysourcenumbers = [];
        foreach($items as $item){
            if(str_contains($item->source_number,'.'))
            {

                $substrclass = substr($item->source_number,0,strpos($item->source_number,'.'));
                if(!in_array($substrclass,$arraysourcenumbers)){
                    $arraysourcenumbers[$substrclass]=$substrclass;
                }
            }
            $arraysourcenumbers[$item->$selectedattribute]=$item->source_number;
        }
        return $arraysourcenumbers;
    }
}
