<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\DateFilter;

class DateCreated extends DateFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \Illuminate\Support\Carbon  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        // Use the $value (selected date) to filter the 'created_at' field
        return $query->whereDate('created_at', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'today' => 'Today',
            'yesterday' => 'Yesterday',
            'thisWeek' => 'This Week',
            // Add more predefined date options if needed
        ];
    }
}
