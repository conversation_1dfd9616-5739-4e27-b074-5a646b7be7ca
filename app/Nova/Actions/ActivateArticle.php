<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Text;

class ActivateArticle extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'تفعيل';

    public function shownOnTableRow()
    {
        return true;
    }




    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */


    public function handle(ActionFields $fields, Collection $models)
    {


        foreach($models as $model){
            if(auth()->user()->id!=$model->user_id || auth()->user()->role=='developer'){
                $validate=['is_valid' => 1];
                $model->forceFill($validate)->update();
                $this->markAsFinished($model);
            }
        }
    }


}
