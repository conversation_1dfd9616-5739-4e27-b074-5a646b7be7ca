<?php

namespace App\Nova\Actions;

use Allanvb\NovaExports\NovaExportAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use Illuminate\Support\Str;

abstract class Exportxlsx extends NovaExportAction
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    abstract protected function getBuiltColumnList(ActionFields $fields): array;
    abstract protected function getQueryData(array $columns, ActionFields $fields);
    abstract public function handle(ActionFields $fields): array;
    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }
    protected function getActionName(): string
    {
        return __('تحميل ملف xlsx') ;
    }
    public function name(): string
    {
        return $this->name ?: $this->getActionName();
    }

}
