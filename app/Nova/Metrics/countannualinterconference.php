<?php

namespace App\Nova\Metrics;

use App\Models\AnnualInterConference;
use App\Models\Filesource;
use App\Models\Modelgroup;
use Lara<PERSON>\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Metrics\Value;

class countannualinterconference extends Value
{
    /**
     * Calculate the value of the metric.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return $this->count($request, AnnualInterConference::class )->format('0,000')->prefix('('.$this->calculdataexcel().' : Excel  )');
    }
    public function calculdataexcel(){
        $nbitem = 0;
        $modelid=Modelgroup::where('class','E')->first();
        $data = Filesource::where('modelgroups_id',$modelid->id)->get();
        foreach($data as $item){
            $nbitem += $item->exceldata;
        }
        return $nbitem;
    }
    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            'ALL'=>'ALL',
            30 => __('30 Days'),
            60 => __('60 Days'),
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return  \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
    public function name()
    {
        return __('AnnualInterConference');
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'countannualinterconference';
    }
}
