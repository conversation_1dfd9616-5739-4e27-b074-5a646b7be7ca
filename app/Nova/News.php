<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Manogi\Tiptap\Tiptap;
use Whitecube\NovaFlexibleContent\Flexible;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class News extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\News::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')
                ->sortable()
                ->hideFromIndex(),

            Text::make(__('title'),'title')
                ->placeholder(__('title'))
                ->rules('required', 'max:255', function ($attribute, $value, $fail) {
                    if ($value && \App\Services\XSSProtectionService::detectXSS($value)) {
                        $fail('The title contains potentially dangerous content.');
                    }
                }),

            Text::make(__('subtitle'),'subtitle')
                ->placeholder(__('subtitle'))
                ->rules('max:500', function ($attribute, $value, $fail) {
                    if ($value && \App\Services\XSSProtectionService::detectXSS($value)) {
                        $fail('The subtitle contains potentially dangerous content.');
                    }
                }),

            Image::make(__('mainimage'), 'mainimage')
                ->disk('public')
                ->path('news/main')
                ->acceptedTypes('.png,.jpg,.jpeg,.webp')
                ->rules('file', 'mimes:png,jpg,jpeg,webp', 'max:10240', function ($attribute, $value, $fail) {
                    if ($value) {
                        try {
                            \App\Services\FileValidationService::validateFile($value, [
                                'allowed_extensions' => ['png', 'jpg', 'jpeg', 'webp'],
                                'allowed_mime_types' => ['image/png', 'image/jpg', 'image/jpeg', 'image/webp'],
                                'max_size' => 10240 * 1024 // 10MB
                            ]);
                        } catch (\Illuminate\Validation\ValidationException $e) {
                            $fail($e->getMessage());
                        }
                    }
                }),

            // Flexible::make('additional_images')->addLayout('Image', 'image', [
            //     Image::make('Image', 'image_file')
            //         ->disk('public')
            //         ->path('news/additional')
            //         ->acceptedTypes('.png,.jpg,.jpeg,.webp,.gif')
            //         ->rules('file', 'mimes:png,jpg,jpeg,webp,gif', 'max:51200'),
            // ])
            // ->button('Add Image')
            // ->limit(5)
            // ->nullable(),
            

            //Textarea::make(__('description'),'description')->placeholder(__('description')),
            Tiptap::make(__('description'), 'description')
                ->buttons(['heading','|','textAlign','|','italic','|','bold','|',
                    'underline','|','link','|','bulletList','|','orderedList','|','textAlign','|',
                    'image','|','table','|','rtl','|','history','|','CodeBlock','|','editHtml',
                ])
                ->headingLevels([1, 2, 3, 4])
                ->placeholder(__('description'))
                ->rules('required', function ($attribute, $value, $fail) {
                    if ($value) {
                        // Clean the rich text content
                        $cleanedValue = \App\Services\XSSProtectionService::sanitizeRichText($value);
                        if ($cleanedValue !== $value) {
                            $fail('The description contains potentially dangerous content that has been removed.');
                        }
                    }
                }),

            Boolean::make(__('status'),'status')
                ->placeholder(__('status')),

            DateTime::make(__('published_until'),'published_until')
                ->placeholder(__('published_until')),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
