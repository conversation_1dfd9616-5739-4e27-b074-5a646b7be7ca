<?php

namespace App\Nova;

use App\Nova\Filters\Classnumber;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Str;

class Sourcegroup extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Sourcegroup::class;

    public static function label()
    {
        return __('المصادر');
    }
    public static function singularLabel()
    {
        return __('مصدر');
    }
    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'sourcetitle';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'groupname',
        'groupnumber',
        'sourcetitle',
        'sourcetitlenumber',
    ];

    //public static $order = ['groupnumber'];


    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {

        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make(__('Group Name'), 'groupname')
                ->displayUsing(function($dat) {return Str::substr($dat,0,20)."...";})
                ->onlyOnIndex(),

            Text::make(__('Group Name'), 'groupname')
                ->hideFromIndex(),

            Text::make(__('Group Number'), 'groupnumber')
                ->sortable(),

            Text::make(__('Source Title'), 'sourcetitle'),

            Text::make(__('Source Title Number'), 'sourcetitlenumber'),

            HasMany::make('Magazines','magazines')
                ->showOnDetail($this->groupnumber=='I'),

            HasMany::make('Inter Conference','interconferences')
                ->showOnDetail($this->groupnumber=='F'),

            HasMany::make('Annual Inter Conference','annualinterconferences')
                ->showOnDetail($this->groupnumber=='E'),

            HasMany::make('Inter Standard','interstandards')
                ->showOnDetail($this->groupnumber=='A'),

            HasMany::make('University Newsletters','universitynewsletters')
                ->showOnDetail($this->groupnumber=='H'),

            HasMany::make('Jurisprudences','jurisprudences')
                ->showOnDetail($this->groupnumber=='B'),

            HasMany::make('Inter Conference Recommendations','interconferencesrecommendations')
                ->showOnDetail($this->groupnumber=='C'),

            HasMany::make('Legal Bodies','legalbodies')
                ->showOnDetail($this->groupnumber=='D'),

            HasMany::make('Books','books')
                ->showOnDetail($this->groupnumber=='G'),

            HasMany::make('Regulations','regulations')
                ->showOnDetail($this->groupnumber=='K'),

            HasMany::make('Reports','reports')
                ->showOnDetail($this->groupnumber=='L'),

            HasMany::make('Researches','researches')
                ->showOnDetail($this->groupnumber=='J'),

            HasMany::make('Participers','participers'),

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            new Classnumber
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
