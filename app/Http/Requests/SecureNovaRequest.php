<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Services\XSSProtectionService;

class SecureNovaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->can('viewNova');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // General security rules
            '*' => ['string', 'max:10000'], // Prevent extremely large inputs
            
            // Specific field validations
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\u0600-\u06FF]+$/', // Allow Arabic and Latin characters
                function ($attribute, $value, $fail) {
                    if (XSSProtectionService::detectXSS($value)) {
                        $fail('The ' . $attribute . ' contains potentially dangerous content.');
                    }
                },
            ],
            
            'email' => [
                'required',
                'email:rfc,dns',
                'max:254',
                function ($attribute, $value, $fail) {
                    if (XSSProtectionService::detectXSS($value)) {
                        $fail('The ' . $attribute . ' contains potentially dangerous content.');
                    }
                },
            ],
            
            'password' => [
                'required',
                'string',
                'min:10',
                'max:128', // Prevent extremely long passwords
                'confirmed',
                function ($attribute, $value, $fail) {
                    // Enhanced password validation
                    if (!preg_match('/[a-z]/', $value)) {
                        $fail(__('كلمة السر يجب أن تحتوي على حرف صغير واحد على الأقل.'));
                        return;
                    }
                    
                    if (!preg_match('/[A-Z]/', $value)) {
                        $fail(__('كلمة السر يجب أن تحتوي على حرف كبير واحد على الأقل.'));
                        return;
                    }
                    
                    if (!preg_match('/[0-9]/', $value)) {
                        $fail(__('كلمة السر يجب أن تحتوي على رقم واحد على الأقل.'));
                        return;
                    }
                    
                    if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $value)) {
                        $fail(__('كلمة السر يجب أن تحتوي على رمز خاص واحد على الأقل (!@#$%^&*).'));
                        return;
                    }
                    
                    // Check for common weak passwords
                    $weakPasswords = [
                        'password123!', 'Password123!', 'admin123!', 'user123!',
                        'qwerty123!', '123456789!', 'welcome123!'
                    ];
                    
                    if (in_array($value, $weakPasswords)) {
                        $fail('كلمة السر ضعيفة جداً. يرجى اختيار كلمة سر أقوى.');
                        return;
                    }
                    
                    // Check for XSS in password (shouldn't happen but extra safety)
                    if (XSSProtectionService::detectXSS($value)) {
                        $fail('كلمة السر تحتوي على محتوى خطير.');
                        return;
                    }
                },
            ],
            
            'role' => [
                'required',
                Rule::in(['user', 'validator', 'admin', 'superadmin']),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'الاسم يجب أن يحتوي على أحرف فقط.',
            'email.email' => 'يجب إدخال عنوان بريد إلكتروني صالح.',
            'email.dns' => 'عنوان البريد الإلكتروني غير صالح.',
            'password.min' => 'كلمة السر يجب أن تحتوي على 10 أحرف على الأقل.',
            'password.confirmed' => 'تأكيد كلمة السر غير متطابق.',
            'role.in' => 'الدور المحدد غير صالح.',
            '*.max' => 'الحقل كبير جداً.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional security checks
            $this->checkForSuspiciousPatterns($validator);
            $this->checkRequestFrequency($validator);
        });
    }

    /**
     * Check for suspicious patterns in the request
     */
    private function checkForSuspiciousPatterns($validator): void
    {
        $suspiciousPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/onclick\s*=/i',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/data:text\/html/i',
            '/eval\s*\(/i',
            '/expression\s*\(/i',
        ];

        foreach ($this->all() as $key => $value) {
            if (is_string($value)) {
                foreach ($suspiciousPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $validator->errors()->add($key, "الحقل {$key} يحتوي على محتوى مشبوه.");
                        
                        // Log the attempt
                        \Log::warning('Suspicious pattern detected in Nova request', [
                            'field' => $key,
                            'pattern' => $pattern,
                            'value' => substr($value, 0, 100),
                            'ip' => $this->ip(),
                            'user_id' => auth()->id(),
                        ]);
                        break;
                    }
                }
            }
        }
    }

    /**
     * Check request frequency to prevent spam
     */
    private function checkRequestFrequency($validator): void
    {
        $key = 'nova_request_frequency:' . $this->ip() . ':' . auth()->id();
        $attempts = cache()->get($key) ?: 0;
        
        if ($attempts > 60) { // More than 60 requests in the last minute (increased from 10)
            $validator->errors()->add('general', 'تم إرسال طلبات كثيرة جداً. يرجى الانتظار قليلاً.');
            
            // \Log::warning('High frequency Nova requests detected', [
            //     'ip' => $this->ip(),
            //     'user_id' => auth()->id(),
            //     'attempts' => $attempts,
            // ]);
        }
        
        cache()->put($key, $attempts + 1, 60); // Cache for 1 minute
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        // Log failed validation attempts for security monitoring
        // \Log::warning('Nova request validation failed', [
        //     'errors' => $validator->errors()->toArray(),
        //     'ip' => $this->ip(),
        //     'user_id' => auth()->id(),
        //     'url' => $this->fullUrl(),
        //     'input' => $this->except(['password', 'password_confirmation']),
        // ]);

        parent::failedValidation($validator);
    }
}
