<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SkipPostSizeValidationForNova
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if this is a Nova route
        if ($this->isNovaRoute($request)) {
            // For Nova routes, skip all post size validation
            return $next($request);
        }

        // For non-Nova routes, apply standard Laravel validation
        $maxSize = $this->getPostMaxSize();

        if ($maxSize > 0 && $request->server('CONTENT_LENGTH') > $maxSize) {
            throw new \Illuminate\Http\Exceptions\PostTooLargeException;
        }

        return $next($request);
    }

    /**
     * Check if this is a Nova route
     */
    private function isNovaRoute(Request $request): bool
    {
        $path = $request->path();
        
        return str_starts_with($path, 'nova-api/') || 
               str_starts_with($path, 'fi-backend/') ||
               str_starts_with($path, 'nova/') ||
               str_contains($path, '/nova/');
    }

    /**
     * Determine the server 'post_max_size' as bytes.
     *
     * @return int
     */
    protected function getPostMaxSize()
    {
        if (is_numeric($postMaxSize = ini_get('post_max_size'))) {
            return (int) $postMaxSize;
        }

        $metric = strtoupper(substr($postMaxSize, -1));
        $postMaxSize = (int) $postMaxSize;

        switch ($metric) {
            case 'K':
                return $postMaxSize * 1024;
            case 'M':
                return $postMaxSize * 1048576;
            case 'G':
                return $postMaxSize * 1073741824;
            default:
                return $postMaxSize;
        }
    }
}
