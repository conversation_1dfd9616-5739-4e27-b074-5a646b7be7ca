<?php

namespace App\Http\Middleware;

use App\Services\AuthorizationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ImportAuthorization
{
    /**
     * Handle an incoming request for import functionality.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        // Import functionality requires authentication
        if (!$user) {
            Log::warning('Unauthorized import access attempt - no authentication', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'method' => $request->method()
            ]);
            
            return redirect()->route('login')
                ->with('error', __('authentication.login_required_import'));
        }
        
        // Check if user has permission for import functionality
        if (!AuthorizationService::canAccessFeature('import', $user)) {
            Log::warning('Import access denied - insufficient privileges', [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'method' => $request->method()
            ]);
            
            abort(403, 'Access denied. Import functionality requires administrator privileges.');
        }
        
        // Additional security checks for import operations
        $this->performSecurityChecks($request, $user);
        
        // Log successful import access
        Log::info('Import access granted', [
            'user_id' => $user->id,
            'user_role' => $user->role,
            'ip' => $request->ip(),
            'url' => $request->fullUrl(),
            'method' => $request->method()
        ]);
        
        return $next($request);
    }
    
    /**
     * Perform additional security checks for import operations
     *
     * @param Request $request
     * @param mixed $user
     * @return void
     */
    private function performSecurityChecks(Request $request, $user): void
    {
        // Check session security
        if (!AuthorizationService::isSessionSecure($request)) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            Log::warning('Import access blocked - session security violation', [
                'user_id' => $user->id,
                'ip' => $request->ip()
            ]);
            
            abort(403, 'Session security violation detected. Please log in again.');
        }
        
        // Rate limiting for import operations
        $this->applyImportRateLimit($request);
    }
    
    /**
     * Apply rate limiting specific to import operations
     *
     * @param Request $request
     * @return void
     */
    private function applyImportRateLimit(Request $request): void
    {
        $key = 'import_access:' . Auth::id() . ':' . $request->ip();
        
        // Allow 10 import operations per hour per user
        $maxAttempts = 10;
        $decayMinutes = 60;
        
        if (\Illuminate\Support\Facades\RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            Log::warning('Import rate limit exceeded', [
                'user_id' => Auth::id(),
                'ip' => $request->ip(),
                'url' => $request->fullUrl()
            ]);
            
            abort(429, 'Too many import operations. Please wait before trying again.');
        }
        
        \Illuminate\Support\Facades\RateLimiter::hit($key, $decayMinutes * 60);
    }
}
