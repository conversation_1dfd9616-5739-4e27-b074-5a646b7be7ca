<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class FixFlexibleContentData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only process Nova API requests
        if (!str_starts_with($request->path(), 'nova-api/')) {
            return $next($request);
        }

        // Skip processing if there are file uploads to avoid interfering with file handling
        if ($request->hasFile('*')) {
            return $next($request);
        }

        // Get all request data
        $data = $request->all();

        // Fix flexible content fields that might be null or causing issues
        $data = $this->fixFlexibleContentFields($data);

        // Replace the request data
        $request->replace($data);

        return $next($request);
    }

    /**
     * Fix flexible content fields to prevent null pointer errors
     *
     * @param array $data
     * @return array
     */
    private function fixFlexibleContentFields(array $data): array
    {
        // List of known flexible content fields
        $flexibleFields = [
            'additional_images',
            'content',
            'flexible_content',
        ];

        foreach ($flexibleFields as $field) {
            if (array_key_exists($field, $data)) {
                $data[$field] = $this->normalizeFlexibleValue($data[$field]);
            }
        }

        // Also check for any fields that contain __flexible__ pattern
        foreach ($data as $key => $value) {
            if (str_contains($key, '__flexible__')) {
                $data[$key] = $this->normalizeFlexibleValue($value);
            }
        }

        return $data;
    }

    /**
     * Normalize a flexible content value to ensure it's always a proper array
     *
     * @param mixed $value
     * @return array
     */
    private function normalizeFlexibleValue($value): array
    {
        // If it's null or empty, return empty array
        if ($value === null || $value === '' || $value === 'null') {
            return [];
        }

        // If it's already an array, return as is
        if (is_array($value)) {
            return $value;
        }

        // If it's a string, try to decode as JSON
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return $decoded;
            }
            
            // Handle special string cases
            if ($value === '[]') {
                return [];
            }
        }

        // For any other type, return empty array
        return [];
    }
}
