<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class language
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $locale = $request->segment(1);

        $additional_languages = [
            'ar' => 'Arabic',
        ];

        if (array_key_exists($locale, $additional_languages)) {
            app()->setLocale($locale);
        }
        return $next($request);
    }
}
