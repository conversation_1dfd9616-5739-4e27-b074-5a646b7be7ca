<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use App\Services\XSSProtectionService;

class NovaSecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply to Nova API routes
        if (!$this->isNovaApiRoute($request)) {
            return $next($request);
        }

        // 1. Enhanced Rate Limiting
        $this->applyRateLimiting($request);

        // 2. Request Size Validation - Handled by NovaFileUploadLimits middleware
        // $this->validateRequestSize($request);

        // 3. Enhanced CSRF Protection
        $this->validateCsrfToken($request);

        // 4. Input Validation and Sanitization
        $this->validateAndSanitizeInput($request);

        // 5. Security Headers
        $response = $next($request);
        $this->addSecurityHeaders($response);

        // // 6. Log Security Events
        // $this->logSecurityEvent($request);

        return $response;
    }

    /**
     * Check if this is a Nova API route
     */
    private function isNovaApiRoute(Request $request): bool
    {
        return $request->is('*/nova-api/*') || $request->is('nova-api/*');
    }

    /**
     * Apply enhanced rate limiting
     */
    private function applyRateLimiting(Request $request): void
    {
        $key = 'nova-api:' . $request->ip() . ':' . Auth::id();
        
        // Different limits for different operations - Made more lenient
        if ($request->isMethod('POST') || $request->isMethod('PUT') || $request->isMethod('DELETE')) {
            // More lenient limits for write operations
            $maxAttempts = 100;  // Increased from 30 to 100
            $decayMinutes = 1;
        } else {
            // Very lenient for read operations
            $maxAttempts = 300;  // Increased from 100 to 300
            $decayMinutes = 1;
        }

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            // Log::warning('Nova API rate limit exceeded', [
            //     'ip' => $request->ip(),
            //     'user_id' => Auth::id(),
            //     'url' => $request->fullUrl(),
            //     'method' => $request->method()
            // ]);
            
            abort(429, 'Too many requests. Please slow down.');
        }

        RateLimiter::hit($key, $decayMinutes * 60);
    }

    /**
     * Validate request size
     */
    private function validateRequestSize(Request $request): void
    {
        $maxSize = 50 * 1024 * 1024; // 50MB

        // Get content length safely
        $contentLength = $request->header('Content-Length');

        // Skip validation if Content-Length is not set or invalid
        if (!$contentLength || !is_numeric($contentLength)) {
            return;
        }

        $contentLength = (int) $contentLength;

        if ($contentLength > $maxSize) {
            // Log::warning('Nova API request too large', [
            //     'ip' => $request->ip(),
            //     'user_id' => Auth::id(),
            //     'size' => $contentLength,
            //     'url' => $request->fullUrl()
            // ]);

            abort(413, 'Request entity too large');
        }
    }

    /**
     * Enhanced CSRF validation for Nova API
     */
    private function validateCsrfToken(Request $request): void
    {
        // Skip CSRF for GET requests
        if ($request->isMethod('GET')) {
            return;
        }

        // Check for CSRF token in header or request
        $token = $request->header('X-CSRF-TOKEN') ?: $request->input('_token');
        
        if (!$token || !hash_equals(session()->token(), $token)) {
            // Log::warning('Nova API CSRF token mismatch', [
            //     'ip' => $request->ip(),
            //     'user_id' => Auth::id(),
            //     'url' => $request->fullUrl(),
            //     'method' => $request->method(),
            //     'has_token' => !empty($token)
            // ]);
            
            abort(419, 'CSRF token mismatch');
        }
    }

    /**
     * Validate and sanitize input data
     */
    private function validateAndSanitizeInput(Request $request): void
    {
        $input = $request->all();
        
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                // Check for XSS patterns
                if (XSSProtectionService::detectXSS($value)) {
                    Log::warning('Nova API XSS attempt detected', [
                        'ip' => $request->ip(),
                        'user_id' => Auth::id(),
                        'field' => $key,
                        'value' => substr($value, 0, 200),
                        'url' => $request->fullUrl()
                    ]);

                    abort(422, "Malicious content detected in field: {$key}");
                }

                // Sanitize the value
                $sanitized = XSSProtectionService::sanitizeInput($value, [
                    'allow_html' => false,
                    'strict_mode' => true
                ]);

                // Replace in request
                $request->merge([$key => $sanitized]);
            }
        }
    }

    /**
     * Add security headers to response
     */
    private function addSecurityHeaders($response): void
    {
        if (!method_exists($response, 'header')) {
            return;
        }

        $response->header('X-Content-Type-Options', 'nosniff');
        $response->header('X-Frame-Options', 'DENY');
        $response->header('X-XSS-Protection', '1; mode=block');
        $response->header('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    }

    /**
     * Log security events for monitoring
     */
    // private function logSecurityEvent(Request $request): void
    // {
    //     // Log all Nova API access for security monitoring
    //     Log::info('Nova API access', [
    //         'ip' => $request->ip(),
    //         'user_id' => Auth::id(),
    //         'method' => $request->method(),
    //         'url' => $request->fullUrl(),
    //         'user_agent' => $request->userAgent(),
    //         'timestamp' => now()->toISOString()
    //     ]);
    // }
}
