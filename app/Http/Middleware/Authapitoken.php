<?php

namespace App\Http\Middleware;


use Closure;
use Illuminate\Http\Request;

use const Grpc\STATUS_ABORTED;

class Authapitoken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {

        $authorisation = $request->header('authorization');

        if($authorisation=='Bearer '.env('DEFAULT_API_AUTHORIZATION_BRIDGE'))
        return $next($request);
        else
        abort(403);
        return ;
    }
}
