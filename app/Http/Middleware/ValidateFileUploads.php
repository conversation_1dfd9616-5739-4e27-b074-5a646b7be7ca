<?php

namespace App\Http\Middleware;

use App\Services\FileValidationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Validation\ValidationException;

class ValidateFileUploads
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only validate file uploads for Nova API routes and specific routes
        if ($this->shouldValidateRequest($request)) {
            $this->validateAllFileUploads($request);
        }

        return $next($request);
    }

    /**
     * Determine if the request should be validated
     *
     * @param Request $request
     * @return bool
     */
    private function shouldValidateRequest(Request $request): bool
    {
        $path = $request->path();
        
        // Validate Nova API routes
        if (str_starts_with($path, 'nova-api/')) {
            return true;
        }

        // Validate specific application routes that handle file uploads
        $fileUploadRoutes = [
            'updatedsourcefile',
            'storecontact',
        ];

        foreach ($fileUploadRoutes as $route) {
            if (str_contains($path, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate all file uploads in the request
     *
     * @param Request $request
     * @return void
     * @throws ValidationException
     */
    private function validateAllFileUploads(Request $request): void
    {
        // Check all files in the request
        foreach ($request->allFiles() as $key => $files) {
            if (is_array($files)) {
                foreach ($files as $file) {
                    if ($file instanceof UploadedFile) {
                        $this->validateSingleFile($file, $key);
                    }
                }
            } elseif ($files instanceof UploadedFile) {
                $this->validateSingleFile($files, $key);
            }
        }
    }

    /**
     * Validate a single uploaded file
     *
     * @param UploadedFile $file
     * @param string $fieldName
     * @return void
     * @throws ValidationException
     */
    private function validateSingleFile(UploadedFile $file, string $fieldName): void
    {
        try {
            // Use different validation rules based on field name or file type
            if ($this->isTrixAttachment($fieldName)) {
                FileValidationService::validateTrixFile($file);
            } else {
                FileValidationService::validateFile($file);
            }
        } catch (ValidationException $e) {
            // Re-throw with field-specific error
            throw ValidationException::withMessages([
                $fieldName => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if the file is a Trix attachment
     *
     * @param string $fieldName
     * @return bool
     */
    private function isTrixAttachment(string $fieldName): bool
    {
        return $fieldName === 'attachment' || str_contains($fieldName, 'trix');
    }
}
