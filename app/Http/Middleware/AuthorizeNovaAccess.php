<?php

namespace App\Http\Middleware;

use App\Services\AuthorizationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuthorizeNovaAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if this is a Nova route
        if (!$this->isNovaRoute($request)) {
            return $next($request);
        }

        // Skip authorization for login and authentication routes
        if ($this->isAuthRoute($request)) {
            return $next($request);
        }

        // Check basic Nova access
        if (!AuthorizationService::canAccessNova()) {
            // Log::warning('Unauthorized Nova access attempt', [
            //     'ip' => $request->ip(),
            //     'user_agent' => $request->userAgent(),
            //     'url' => $request->fullUrl(),
            //     'user_id' => Auth::id()
            // ]);
            
            abort(403, 'Access denied to Nova dashboard');
        }

        // Check session security
        if (!AuthorizationService::isSessionSecure($request)) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            return redirect('/login')->with('error', 'Session security violation detected. Please log in again.');
        }

        // Check if session should be terminated
        if (AuthorizationService::shouldTerminateSession($request)) {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            return redirect('/login')->with('error', 'Session expired. Please log in again.');
        }

        // Check resource-specific permissions for API routes
        if ($this->isNovaApiRoute($request)) {
            $this->checkResourcePermissions($request);
        }

        return $next($request);
    }

    /**
     * Check if the request is for a Nova route
     *
     * @param Request $request
     * @return bool
     */
    private function isNovaRoute(Request $request): bool
    {
        $path = $request->path();

        return str_starts_with($path, 'fi-backend') ||
               str_starts_with($path, 'nova-api/') ||
               str_starts_with($path, 'nova-vendor/');
    }

    /**
     * Check if the request is for an authentication route that should bypass authorization
     *
     * @param Request $request
     * @return bool
     */
    private function isAuthRoute(Request $request): bool
    {
        $path = $request->path();

        // Allow access to login and authentication routes
        $authRoutes = [
            'fi-backend/login',
            'fi-backend/logout',
            'fi-backend/password/reset',
            'fi-backend/password/email',
        ];

        foreach ($authRoutes as $route) {
            if (str_starts_with($path, $route)) {
                return true;
            }
        }

        // Also allow the main Nova login page
        if ($path === 'fi-backend') {
            return true;
        }

        return false;
    }

    /**
     * Check if the request is for a Nova API route
     *
     * @param Request $request
     * @return bool
     */
    private function isNovaApiRoute(Request $request): bool
    {
        return str_starts_with($request->path(), 'nova-api/');
    }

    /**
     * Check resource-specific permissions
     *
     * @param Request $request
     * @return void
     */
    private function checkResourcePermissions(Request $request): void
    {
        $path = $request->path();
        $method = $request->method();
        
        // Extract resource and action from the path
        $resource = $this->extractResourceFromPath($path);
        $action = $this->mapMethodToAction($method, $path);
        
        if ($resource && !AuthorizationService::canAccess($resource, $action)) {
            // Log::warning('Unauthorized Nova resource access', [
            //     'user_id' => Auth::id(),
            //     'resource' => $resource,
            //     'action' => $action,
            //     'path' => $path,
            //     'method' => $method,
            //     'ip' => $request->ip()
            // ]);
            
            abort(403, "Access denied to {$resource} resource");
        }
    }

    /**
     * Extract resource name from Nova API path
     *
     * @param string $path
     * @return string|null
     */
    private function extractResourceFromPath(string $path): ?string
    {
        // Nova API paths are typically: nova-api/{resource}/{id?}/{action?}
        $segments = explode('/', $path);
        
        if (count($segments) >= 2 && $segments[0] === 'nova-api') {
            return $segments[1];
        }
        
        return null;
    }

    /**
     * Map HTTP method and path to Nova action
     *
     * @param string $method
     * @param string $path
     * @return string
     */
    private function mapMethodToAction(string $method, string $path): string
    {
        $segments = explode('/', $path);
        
        // Check for specific actions in the path
        if (str_contains($path, '/delete')) {
            return 'delete';
        }
        
        if (str_contains($path, '/restore')) {
            return 'restore';
        }
        
        if (str_contains($path, '/force-delete')) {
            return 'forceDelete';
        }
        
        if (str_contains($path, '/replicate')) {
            return 'replicate';
        }
        
        if (str_contains($path, '/attach') || str_contains($path, '/detach')) {
            return 'update';
        }

        // Map HTTP methods to actions
        switch (strtoupper($method)) {
            case 'GET':
                // Check if it's a specific resource (has ID)
                if (count($segments) >= 3 && is_numeric($segments[2])) {
                    return 'view';
                }
                return 'index';
                
            case 'POST':
                return 'create';
                
            case 'PUT':
            case 'PATCH':
                return 'update';
                
            case 'DELETE':
                return 'delete';
                
            default:
                return 'view';
        }
    }
}
