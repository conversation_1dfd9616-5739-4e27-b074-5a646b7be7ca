<?php

namespace App\Http\Middleware;

use App\Services\XSSProtectionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class XSSProtection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip XSS protection for static assets
        if ($this->isStaticAsset($request)) {
            return $next($request);
        }

        // Sanitize input data
        $this->sanitizeRequestData($request);

        $response = $next($request);

        // Add security headers
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Sanitize request data to prevent XSS
     *
     * @param Request $request
     * @return void
     */
    private function sanitizeRequestData(Request $request): void
    {
        // Skip sanitization if there are file uploads to avoid interfering with file handling
        if ($request->hasFile('*')) {
            return;
        }

        $input = $request->all();
        $sanitized = $this->sanitizeArray($input, $request);

        // Replace request data with sanitized data
        $request->replace($sanitized);
    }

    /**
     * Recursively sanitize array data
     *
     * @param array $data
     * @param Request $request
     * @return array
     */
    private function sanitizeArray(array $data, Request $request): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value, $request);
            } else {
                $sanitized[$key] = $this->sanitizeValue($key, $value, $request);
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize individual value based on field type
     *
     * @param string $key
     * @param mixed $value
     * @param Request $request
     * @return mixed
     */
    private function sanitizeValue(string $key, $value, Request $request)
    {
        if (!is_string($value)) {
            return $value;
        }

        // Skip sanitization for certain fields that need raw content
        if ($this->shouldSkipSanitization($key, $request)) {
            return $value;
        }

        // Detect potential XSS
        if (XSSProtectionService::detectXSS($value)) {
            Log::warning('XSS Protection: Potential XSS detected and sanitized', [
                'field' => $key,
                'original_value' => substr($value, 0, 200),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'user_id' => auth()->id(),
                'action' => 'sanitized'
            ]);

            // For contact forms and user-facing forms, sanitize instead of blocking
            if ($this->isUserFacingForm($request)) {
                // Sanitize the content and continue processing
                $value = XSSProtectionService::sanitizeInput($value, [
                    'allow_html' => false,
                    'strict_mode' => true
                ]);

                // Log the sanitization
                Log::info('XSS Protection: Content sanitized for user form', [
                    'field' => $key,
                    'sanitized_value' => substr($value, 0, 200),
                    'url' => $request->fullUrl()
                ]);
            } else {
                // For admin/API endpoints, still block if configured
                if (config('xss_protection.validation.block_suspicious_content', false)) {
                    abort(422, 'Suspicious content detected in field: ' . $key);
                }
            }
        }

        // Get sanitization rules for this field
        $rules = $this->getSanitizationRules($key);

        return XSSProtectionService::sanitizeInput($value, $rules);
    }

    /**
     * Determine if a field should skip sanitization
     *
     * @param string $key
     * @param Request $request
     * @return bool
     */
    private function shouldSkipSanitization(string $key, Request $request): bool
    {
        // Skip for password fields
        if (in_array($key, ['password', 'password_confirmation', 'current_password'])) {
            return true;
        }

        // Skip for CSRF tokens
        if ($key === '_token') {
            return true;
        }

        // Skip for certain Nova fields that handle their own sanitization
        if (str_starts_with($request->path(), 'nova-api/') && in_array($key, ['_method', 'viaResource', 'viaResourceId', 'viaRelationship'])) {
            return true;
        }

        return false;
    }

    /**
     * Get sanitization rules for a specific field
     *
     * @param string $key
     * @return array
     */
    private function getSanitizationRules(string $key): array
    {
        $rules = config('xss_protection.sanitization_rules', []);

        // Check for exact field name match
        if (isset($rules[$key])) {
            return $rules[$key];
        }

        // Check for pattern matches
        if (str_contains($key, 'description') || str_contains($key, 'content') || str_contains($key, 'body')) {
            return $rules['description'] ?? ['allow_html' => true, 'strict_mode' => false];
        }

        if (str_contains($key, 'title') || str_contains($key, 'name')) {
            return $rules['title'] ?? ['allow_html' => false, 'strict_mode' => true];
        }

        if (str_contains($key, 'email')) {
            return $rules['email'] ?? ['allow_html' => false, 'strict_mode' => true];
        }

        if (str_contains($key, 'url') || str_contains($key, 'link')) {
            return $rules['url'] ?? ['allow_html' => false, 'strict_mode' => true];
        }

        if (str_contains($key, 'search') || str_contains($key, 'query')) {
            return $rules['search'] ?? ['allow_html' => false, 'strict_mode' => true];
        }

        // Default rules
        return ['allow_html' => false, 'strict_mode' => false];
    }

    /**
     * Check if this is a user-facing form (contact, registration, etc.)
     *
     * @param Request $request
     * @return bool
     */
    private function isUserFacingForm(Request $request): bool
    {
        $userFacingRoutes = [
            'contact',
            'storecontact',
            'register',
            'login',
            'newsletter',
            'subscribe'
        ];

        $currentRoute = $request->route() ? $request->route()->getName() : '';
        $path = $request->path();

        // Check route name
        foreach ($userFacingRoutes as $route) {
            if (str_contains($currentRoute, $route) || str_contains($path, $route)) {
                return true;
            }
        }

        // Check if it's not an admin/nova route
        return !str_contains($path, 'nova') && !str_contains($path, 'admin');
    }

    /**
     * Check if the request is for a static asset
     *
     * @param Request $request
     * @return bool
     */
    private function isStaticAsset(Request $request): bool
    {
        $path = $request->path();

        // Static asset extensions
        $staticExtensions = [
            'css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'webp',
            'ico', 'woff', 'woff2', 'ttf', 'eot', 'otf', 'map',
            'pdf', 'zip', 'rar', 'mp3', 'mp4', 'avi', 'mov'
        ];

        // Check if path ends with static extension
        foreach ($staticExtensions as $ext) {
            if (str_ends_with($path, '.' . $ext)) {
                return true;
            }
        }

        // Check for common static asset paths
        $staticPaths = [
            'css/', 'js/', 'images/', 'img/', 'assets/', 'fonts/',
            'storage/', 'vendor/', 'mix-manifest.json'
        ];

        foreach ($staticPaths as $staticPath) {
            if (str_starts_with($path, $staticPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Add security headers to response
     *
     * @param mixed $response
     * @return void
     */
    private function addSecurityHeaders($response): void
    {
        if (!method_exists($response, 'header')) {
            return;
        }

        // Skip CSP for local development environments
        if (!app()->environment('production')) {
            // Only add basic security headers for local development
            $response->header('X-Content-Type-Options', 'nosniff');
            $response->header('X-Frame-Options', 'SAMEORIGIN');
            return;
        }

        // Add Content Security Policy for production
        $csp = XSSProtectionService::generateCSP();
        $response->header('Content-Security-Policy', $csp);

        // Add other security headers
        $headers = config('xss_protection.security_headers', []);
        foreach ($headers as $name => $value) {
            $response->header($name, $value);
        }
    }
}
