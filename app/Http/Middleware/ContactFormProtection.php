<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;

class ContactFormProtection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only apply to contact form submissions
        if (!$this->isContactFormSubmission($request)) {
            return $next($request);
        }

        // Apply rate limiting
        $this->applyContactFormRateLimit($request);

        // Validate request size
        $this->validateRequestSize($request);

        // Log submission attempt
        $this->logContactFormAttempt($request);

        return $next($request);
    }

    /**
     * Check if this is a contact form submission
     */
    private function isContactFormSubmission(Request $request): bool
    {
        return $request->isMethod('POST') && 
               (str_contains($request->path(), 'storecontact') || 
                str_contains($request->route()->getName() ?? '', 'storecontact'));
    }

    /**
     * Apply rate limiting specific to contact forms
     */
    private function applyContactFormRateLimit(Request $request): void
    {
        $key = 'contact_form:' . $request->ip();
        
        // Allow 3 submissions per 5 minutes per IP
        $maxAttempts = 3;
        $decayMinutes = 5;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            Log::warning('Contact form rate limit exceeded', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl()
            ]);
            
            abort(429, __('validation.contact_form_rate_limit'));
        }

        RateLimiter::hit($key, $decayMinutes * 60);
    }

    /**
     * Validate request size to prevent large payloads
     */
    private function validateRequestSize(Request $request): void
    {
        $maxSize = 1024 * 1024; // 1MB for contact forms
        
        if ($request->header('Content-Length') > $maxSize) {
            Log::warning('Contact form request too large', [
                'ip' => $request->ip(),
                'size' => $request->header('Content-Length'),
                'url' => $request->fullUrl()
            ]);
            
            abort(413, __('validation.contact_form_too_large'));
        }
    }

    /**
     * Log contact form submission attempts
     */
    private function logContactFormAttempt(Request $request): void
    {
        Log::info('Contact form submission attempt', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referer' => $request->header('referer'),
            'has_csrf' => $request->hasHeader('X-CSRF-TOKEN') || $request->has('_token'),
            'content_length' => $request->header('Content-Length', 0)
        ]);
    }
}
