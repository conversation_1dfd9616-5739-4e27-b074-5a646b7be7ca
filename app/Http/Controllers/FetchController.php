<?php

namespace App\Http\Controllers;

use App;
use App\Models\AnnualInterConference;
use App\Models\Book;
use App\Models\Contact;
use App\Models\Filesource;
use App\Models\InterConference;
use App\Models\InterConferenceRecommendation;
use App\Models\InterStandard;
use App\Models\Jurisprudence;
use App\Models\Language;
use App\Models\LegalBody;
use App\Models\Magazine;
use App\Models\Modelgroup;
use App\Models\Regulation;
use App\Models\Report;
use App\Models\Research;
use App\Models\Sourcegroup;
use App\Models\UniversityNewsletter;
use App\Http\Controllers\SearchFields;
use Elasticsearch\ClientBuilder;
use Matchish\ScoutElasticSearch\MixedSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Matchish\ScoutElasticSearch\Engines\ElasticSearchEngine;
use Redirect;
use stdClass;
use Validator;
use Illuminate\Support\Facades\Http;


use ONGR\ElasticsearchDSL\Query\MatchAllQuery;

use ONGR\ElasticsearchDSL\Search;

class FetchController extends Controller
{

  public function index(Request $request)
  {
    $results = [];

    $array_search = [
      (new AnnualInterConference())->searchableAs(),
      (new Book())->searchableAs(),
      (new InterConference())->searchableAs(),
      (new InterConferenceRecommendation())->searchableAs(),
      (new Magazine())->searchableAs(),
      (new Research())->searchableAs(),
      (new UniversityNewsletter())->searchableAs(),
      (new Regulation())->searchableAs(),
      (new Report())->searchableAs(),
      (new InterStandard())->searchableAs(),
      (new Jurisprudence())->searchableAs(),
      (new LegalBody())->searchableAs(),
    ];


    function containsArabicLetters($words, $arabicLetters)
    {
      $wordsUnicode = preg_replace('//u', '', $words);
      $arabicLettersUnicode = array_map(function ($letter) {
        return mb_convert_encoding($letter, 'UTF-8', 'UTF-8');
      }, $arabicLetters);

      foreach ($arabicLettersUnicode as $letter) {
        if (mb_strpos($wordsUnicode, $letter) !== false) {
          return true;
        }
      }

      return false;
    }

    //filter models
    if ($request->input('filter1')) {

      $filter1 = explode(',', $request->input('filter1'));
      $filtermodels = '';
      foreach ($filter1 as $element) {
        if (in_array($element, ['AnnualInterConference', 'Book', 'InterConference', 'InterConferenceRecommendation', 'InterStandard', 'Jurisprudence', 'LegalBody', 'Magazine', 'Regulation', 'Report', 'Research', 'UniversityNewsletter'])) {
          $selctedsearchmodel = (new ('App\Models\\' . $element)())->searchableAs();
          if (in_array($selctedsearchmodel, $array_search, true)) {
            $element = 'App\Models\\' . $element;
            $filtermodels .= ',' . (new $element())->searchableAs();
          }
        } else redirect()->route('index.' . app()->getLocale());
      }
      if ($filtermodels != '') {
        $array_search = explode(',', $filtermodels);
        $array_search = array_filter($array_search);
      }
    }
    //end filter models

    $option_search = '';
    //filter authors
    $option_search_author = '';
    if ($request->input('filter2') && $request->input('filter2') != '') {

      //$filter2 = 'model1,model2,model3';
      $filter2 = explode(',', $request->input('filter2'));
      $filter2_content = '';
      foreach ($filter2 as $element) {
        $element = $this->replace_reserved($element);
        $filter2_content .= ' OR author:"' . $element . '"';
      }
      $filter2_content = substr($filter2_content, 3);
      $option_search_author = ' AND (  ' . $filter2_content . ') ';
    }
    //end filter authors

    //filter year
    $option_search_year = '';
    if ($request->input('year') && $request->input('year') != '') {

      //$filter2 = 'year1,year2,year3';
      $filter3 = explode(',', $request->input('year'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $element = $this->replace_reserved($element);
        $filter3_content .= 'OR year:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_year = ' AND (' . $filter3_content . ') ';
    }
    //end filter year


    //filter language
    $option_search_language = '';
    if ($request->input('language') && $request->input('language') != '') {

      //$filter2 = 'year1,year2,year3';

      $filter3 = explode(',', $request->input('language'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $element = $this->replace_reserved($element);
        $filter3_content .= 'OR language_id:' . (int)$element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_language = ' AND (' . $filter3_content . ') ';
    }
    //end filter language

    //filter type
    $option_search_type = '';
    if ($request->input('types') && $request->input('types') != '') {
      //$filter2 = 'year1,year2,year3';

      $filter3 = explode(',', $request->input('types'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $element = $this->replace_reserved($element);
        if ($element != '')
          $filter3_content .= 'OR type:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      if ($filter3_content != '')
        $option_search_type = ' AND (' . $filter3_content . ') ';
    }
    //end filter type

    //filter subjects
    $option_search_selected_subject = '';
    if ($request->input('selectedsubject') && $request->input('selectedsubject') != '') {
      //$filter2 = 'year1,year2,year3';

      $filtersubject = explode(',', $request->input('selectedsubject'));
      $filtersubject_content = '';
      foreach ($filtersubject as $element) {

        $element = $this->replace_reserved($element);
        if ($element != '')
          $filtersubject_content .= 'OR sourcegroup_id:"' . $element . '" ';
      }
      $filtersubject_content = substr($filtersubject_content, 2);
      $filtersubject_content = substr($filtersubject_content, 1, -1);
      if ($filtersubject_content != '')
        $option_search_selected_subject = ' AND (' . $filtersubject_content . ') ';
    }
    //end filter subjects




    $option_search = $option_search_author . $option_search_year . $option_search_language . $option_search_type . $option_search_selected_subject;
    if (($request->input('search') && strlen(utf8_decode($request->input('search'))) > 2) || $request->input('language') || $request->input('year') || $request->input('author')) {

      $option_search .= " AND is_valid:1";


      $stopWords = array("على", "في", "من", "إلى", "عن");
      $search = $this->replace_reserved($request->input('search'));
      $words = explode(" ", $search);

      $arabicLetters = [
        'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
        'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
      ];
      $modifiedWords = array_map(function ($word) use ($arabicLetters) {
        if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetters($word, $arabicLetters)) {
          $word = "ال" . $word;
        }
        return $word;
      }, $words);
      $search = implode(" ", $modifiedWords);

      if ($search != '') {
        $searchlike =  "*" . $search . "*";
        $searchlike = preg_replace('/\b\w{1,2}\b\s*/u', '', $search);

        $searchWeights = config('search_weights');
        $scoresearch = '(' . $search . ' OR "' . $searchlike . '"^100 OR (';
        foreach ($searchWeights as $field => $weight) {
          $scoresearch .= "{$field}:(\"{$search}\")^{$weight} OR ";
        }
        $scoresearch = rtrim($scoresearch, ' OR ') . ')) ';
      } else {
        $scoresearch = '';
        $option_search = substr($option_search, 4);
      }
      //dd($scoresearch);

      // $tt = MixedSearch::search($scoresearch.$option_search);

      // dd($tt);
      $results = MixedSearch::search(regenerateStemmerSearch($scoresearch . $option_search))
        ->within(implode(',', $array_search))
        ->paginate(10000);
    }
    $models = Modelgroup::all()->sortBy('screenorder');
    $languages = Language::all();


    $subjects = Sourcegroup::all();


    if ($results) {
      $resultauthor = [];
      foreach ($results as $result) {
        if (!in_array($result->author, $resultauthor, true)) {
          if ($result->author != '')
            array_push($resultauthor, $result->author);
        }
      }
      $resultyears = [];
      foreach ($results as $result) {
        if (!in_array($result->year, $resultyears, true)) {
          if ($result->year != '')
            array_push($resultyears, $result->year);
        }
      }
      function clean($word)
      {
        $cleaned = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $word));
        return $cleaned;
      }

      $searchWord = $_GET["search"];
      $cleanedInput = clean($searchWord);
      // $elasticsearchResults = $results->toArray();
      $perPage = 300;
      $total = $results->total();
      $items = $results->items();
      $matchingItems = [];
      $containingItems = [];
      $nonMatchingItems = [];
      $searchingPage = $_GET["pageSearch"];
      $currentPage = $searchingPage;
      foreach ($items as $result) {
        $item = $result->getAttributes();
        // dump($item['author']);
        $title = $result->getTitle();
        $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));

        if ($cleanedTitle == $cleanedInput) {
          $matchingItems[] = $result;
        } elseif (str_contains($cleanedTitle, $cleanedInput)) {
          $containingItems[] = $result;
        } else {
          $nonMatchingItems[] = $result;
        }
      }

      $items = array_merge($matchingItems, $containingItems, $nonMatchingItems);
      $total = count($items);
      $items = array_slice($items, ($searchingPage - 1) * $perPage, $perPage);


      $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
        $items,
        $total,
        $perPage,
        $currentPage,
        ['path' => request()->url(), 'query' => request()->query()]
      );
      $results = $paginatedResults;
      $subjects = Sourcegroup::whereIn('id', array_unique(array_column($results->items(), 'sourcegroup_id')))->get();
      //dd($resultyears);
      $view  = view(
        'fetchfilterresults',
        [
          'results' => $results,
          'search' => $request->input('search'),
          'models' => $models,
          'filter1' => $request->input('filter1'),
          'filter2' => $request->input('filter2'),
          'year' => $request->input('year'),
          'yearsrelease' => $resultyears,
          'language' => $request->input('language'),
          'types2' => $request->input('types'),
          'selectedsubject'   => $request->input('selectedsubject'),
          'subjects' => $subjects,
          'authors' => $resultauthor,
          'languages' => $languages

        ]
      )->render();

      // dd(array_unique(array_column($results->items(),'sourcegroup_id')));
      //  dd(Sourcegroup::whereIn('id',array_unique(array_column($results->items(),'sourcegroup_id')))->get()->map->only('id', 'sourcetitle')->toArray());


      return response()->json(
        [
          'view' => $view,
          'results' => $results,
          'authors' => array_unique(array_map(function ($value) {
            return trim(preg_replace('/\s+/', ' ', $value));
          }, $resultauthor)),

          'languages' => array_unique(array_map(function ($value) {
            return trim(preg_replace('/\s+/', ' ', $value));
          }, $languages->pluck(['id', 'title'])->toArray())),

          'yearsrelease' => array_unique(array_map(function ($value) {
            return trim(preg_replace('/\s+/', ' ', $value));
          }, $resultyears)),



          'subjects' => Sourcegroup::whereIn('id', array_unique(array_column($results->items(), 'sourcegroup_id')))->get()->map->only('id', 'sourcetitle')->toArray(),

        ]



      );
    } else
      return '';
  }
  private function replace_reserved(String $element = null)
  {
    if ($element != null) {
      $search  = array(':', '+', '/', '\\', '?', '<', '>', '"', '\'', '(', ')', '^', '~', '{', '}', '[', '-', ']');
      $element = str_replace($search, '', $element);
      return $element;
    }
    return '';
  }

  public function search(Request $request, $model)
  {

    $results = [];
    $proprities = [];

    $models = Modelgroup::orderBy('screenorder', 'asc')->get();


    $model = Modelgroup::where('slug', $model)->first();
    if ($model) {
      $importmodel = 'App\Models\\' . $model->name;

      $model = $model->name;


      $array_search = [(new $importmodel())->searchableAs()];
    } else {
      $array_search = [
        (new AnnualInterConference())->searchableAs(),
        (new Book())->searchableAs(),
        (new InterConference())->searchableAs(),
        (new InterConferenceRecommendation())->searchableAs(),
        (new Magazine())->searchableAs(),
        (new Research())->searchableAs(),
        (new UniversityNewsletter())->searchableAs(),
        (new Regulation())->searchableAs(),
        (new Report())->searchableAs(),
        (new InterStandard())->searchableAs(),
        (new Jurisprudence())->searchableAs(),
        (new LegalBody())->searchableAs(),
      ];
    }
    $search = '';
    $search =  $request->input('search');
    function containsArabicLetter($words, $arabicLetters)
    {
      $wordsUnicode = preg_replace('//u', '', $words);
      $arabicLettersUnicode = array_map(function ($letter) {
        return mb_convert_encoding($letter, 'UTF-8', 'UTF-8');
      }, $arabicLetters);

      foreach ($arabicLettersUnicode as $letter) {
        if (mb_strpos($wordsUnicode, $letter) !== false) {
          return true;
        }
      }

      return false;
    }
    if ($search) {

      $stopWords = array("على", "في", "من", "إلى", "عن");
      $words = explode(" ", $search);

      $arabicLetters = [
        'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
        'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
      ];
      $modifiedWords = array_map(function ($word) use ($arabicLetters) {
        if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetter($word, $arabicLetters)) {
          $word = "ال" . $word;
        }
        return $word;
      }, $words);

      $search = implode(" ", $modifiedWords);
      if ($search == "على") {
        $search = "result_should_be_null";
      }
    }

    $search = $this->replace_reserved($search);
    //check search filters
    if ($request->input('search') || $request->get('exporter') || $request->get('year') || $request->get('author') || $request->get('language')) {

      $search = $this->replace_reserved($request->input('search')) . '';

      $search_author = '';
      if ($request->get('author') and $request->get('author') != '')
        $search_author = ' AND author:"' . $request->get('author') . '"^5';

      $search_year = '';
      if ($request->get('year') and $request->get('year') != '')
        $search_year = ' AND year:"' . $request->get('year') . '"';

      $search_language = '';
      if ($request->get('language') and $request->get('language'))
        $search_language = ' AND language_id:"' . $request->get('language') . '"';

      $search_exporter = '';
      if ($request->get('exporter') and $request->get('exporter') != '') {

        if ($model == 'Regulation')
          $search_exporter = ' AND country:"' . $request->get('exporter') . '"';

        elseif ($model == 'UniversityNewsletter')
          $search_exporter = ' AND university:"' . $request->get('exporter') . '"';
        else
          $search_exporter = ' AND author:"' . $request->get('exporter') . '"';
      }


      $filtersearch  = $search_author . $search_exporter . $search_year . $search_language;
      $filtersearch .= ' AND is_valid:1';

      if ($search == '') {

        $search = substr($filtersearch, 5);
      } else {
        $searchlike =  "*" . $search . "*";
        $searchlike = preg_replace('/\b\w{1,2}\b\s*/u', '', $search);
        $score = ' ( (' . $search . ') OR

            ("' . $searchlike . '")^100 OR

            title:("' . $search . '")^150 OR
            author:("' . $search . '")^140 OR
            main_title:("' . $search . '")^100 OR
            secondary_title_one:("' . $search . '")^100 OR
            secondary_title_two:("' . $search . '")^100 OR

            organiser:("' . $search . '")^70 OR
            conference_title:("' . $search . '")^70 OR
            session_title:("' . $search . '")^70 OR
            chapter_title:("' . $search . '")^70 OR
            search_title:("' . $search . '")^70 OR
            request_title:("' . $search . '")^70 OR
            publisher:("' . $search . '")^70 OR
            act_title:("' . $search . '")^70 OR
            request_title:("' . $search . '")^70 OR
            publisher:("' . $search . '")^70

          )';

        if ($filtersearch != '')
          $search = '(' . $this->replace_reserved($search) . ' OR "' . $this->replace_reserved($search) . '"^40' . $score . ' ) AND (' . substr($filtersearch, 5) . ')';
      }
      //search results with filters
      $results = MixedSearch::search(regenerateStemmerSearch($search));

      $results = $results->within(implode(',', $array_search))->paginate(10000);
      function clean($word)
      {
        $cleaned = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $word));
        return $cleaned;
      }
      $searchWord = $_GET["search"];
      $cleanedInput = clean($searchWord);
      // $elasticsearchResults = $results->toArray();
      $perPage = 300;
      $total = $results->total();
      $items = $results->items();
      $matchingItems = [];
      $containingItems = [];
      $nonMatchingItems = [];
      $searchingPage = $_GET["pageSearch"];
      $currentPage = $searchingPage;
      foreach ($items as $result) {
        $item = $result->getAttributes();
        $title = $result->getTitle();
        $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));

        if ($cleanedTitle == $cleanedInput) {
          $matchingItems[] = $result;
        } elseif (str_contains($cleanedTitle, $cleanedInput)) {
          $containingItems[] = $result;
        } else {
          $nonMatchingItems[] = $result;
        }
      }
      $items = array_merge($matchingItems, $containingItems, $nonMatchingItems);
      $total = count($items);
      $items = array_slice($items, ($searchingPage - 1) * $perPage, $perPage);
      $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
        $items,
        $total,
        $perPage,
        $currentPage,
        ['path' => request()->url(), 'query' => request()->query()]
      );
      $results = $paginatedResults;

      //count same search filtred results in the other models
      foreach ($models as $modeltype) {
        $importmodelty = 'App\Models\\' . $modeltype->name;
        $arraycount = [(new $importmodelty())->searchableAs(),];
        $model_results[$modeltype->name] = MixedSearch::search(regenerateStemmerSearch($search));
        $model_results[$modeltype->name] = $model_results[$modeltype->name]->within(implode(',', $arraycount))->paginate(25)->total();
      }
      //get filtrs ( author exporter year)
      $proprities = $this->getproprieties($results);
    } else {

      if ($model) {
        $importmodel = 'App\Models\\' . $model;
        $results = $importmodel::where('is_valid', 1)->paginate(25);
      }
      //Modelname::where('status',1)->paginate(25);

      $model_results['AnnualInterConference'] = AnnualInterConference::where('is_valid', 1)->count('id');
      $model_results['Book'] = Book::where('is_valid', 1)->count('id');
      $model_results['InterConference'] = InterConference::where('is_valid', 1)->count('id');
      $model_results['InterConferenceRecommendation'] = InterConferenceRecommendation::where('is_valid', 1)->count('id');
      $model_results['InterStandard'] = InterStandard::where('is_valid', 1)->count('id');
      $model_results['Jurisprudence'] = Jurisprudence::where('is_valid', 1)->count('id');
      $model_results['LegalBody'] = LegalBody::where('is_valid', 1)->count('id');
      $model_results['Magazine'] = Magazine::where('is_valid', 1)->count('id');
      $model_results['Regulation'] = Regulation::where('is_valid', 1)->count('id');
      $model_results['Report'] = Report::where('is_valid', 1)->count('id');
      $model_results['Research'] = Research::where('is_valid', 1)->count('id');
      $model_results['UniversityNewsletter'] = UniversityNewsletter::where('is_valid', 1)->count('id');

      if ($results)
        $proprities = $this->getproprieties($results);
    }


    foreach ($models as $modelitem) {
      if ($modelitem->name == $model)
        $model = $modelitem->title;
    }
    $languages = Language::all();



    if (($request->input('search') && strlen(utf8_decode($request->input('search'))) > 2) || $request->get('exporter') || $request->get('year') || $request->get('author') || $request->get('language')) {

      $view = view(
        'fetchresult',
        [
          'results' => $results,
          'model_results' => $model_results,
          'model' => $model,
          'models' => $models,
          'search' => $request->input('search'),
          'proprities' => $proprities,
          'langauges' => $languages
        ]
      )->render();
      return response()->json(['view' => $view, 'results' => $results]);
    } else
      return '';
  }








  /**
   * Search resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   *
   */
  public function advancedsearch(Request $request)
  {
    $textsearch = "";

    $modelsOptions = [
      "InterStandard"                   => "مجموعة المعيار الدولية",
      "Jurisprudence"                   => "مجموعة قرارات وتوصيات المجامع الفقهية",
      "InterConferenceRecommendation"   => "مجموعة قرارات وتوصيات المؤتمرات الدولية",
      "LegalBody"                       => "مجموعة قرارات وفتاوى الهيئات الشرعية",
      "AnnualInterConference"           => "مجموعة المؤتمرات الدولية الدورية",
      "InterConference"                 => "مجموعة المؤتمرات الدولية",
      "Book"                            => "مجموعة الكتب",
      "UniversityNewsletter"            => "مجموعة الرسائل الجامعية",
      "Magazine"                        => "مجموعة المجلات",
      "Research"                        => "مجموعة الأبحاث",
      "Regulation"                      => "مجموعةالقوانين والتشريعات",
      "Report"                          => "مجموعة التقارير المتعلقة بالصناعة المالية الإسلامية"
    ];



    $models = Modelgroup::all();

    $results = [];
    $array_search = [
      (new AnnualInterConference())->searchableAs(),
      (new Book())->searchableAs(),
      (new InterConference())->searchableAs(),
      (new InterConferenceRecommendation())->searchableAs(),
      (new Magazine())->searchableAs(),
      (new Research())->searchableAs(),
      (new UniversityNewsletter())->searchableAs(),
      (new Regulation())->searchableAs(),
      (new Report())->searchableAs(),
      (new InterStandard())->searchableAs(),
      (new Jurisprudence())->searchableAs(),
      (new LegalBody())->searchableAs(),
    ];
    //filter models
    if ($request->input('filter1')) {
      $filter1 = explode(',', $request->input('filter1'));
      $filtermodels = '';
      foreach ($filter1 as $element) {
        if (in_array($element, ['AnnualInterConference', 'Book', 'InterConference', 'InterConferenceRecommendation', 'InterStandard', 'Jurisprudence', 'LegalBody', 'Magazine', 'Regulation', 'Report', 'Research', 'UniversityNewsletter'])) {
          $selctedsearchmodel = (new ('App\Models\\' . $element)())->searchableAs();
          if (in_array($selctedsearchmodel, $array_search, true)) {
            $element = 'App\Models\\' . $element;
            $filtermodels .= ',' . (new $element())->searchableAs();
          }
        } else redirect()->route('index.' . app()->getLocale());
      }
      if ($filtermodels != '') {
        $array_search = explode(',', $filtermodels);
        $array_search = array_filter($array_search);
      }
    }
    //end filter models


    //filter language
    $option_search_language = '';
    if ($request->input('language') && $request->input('language') != '') {

      $filter3 = explode(',', $request->input('language'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $filter3_content .= 'OR language_id:' . (int)$element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_language = ' AND (' . $filter3_content . ') ';
    }
    //end filter language
    //filter year
    $option_search_year = '';
    if ($request->input('year') && $request->input('year') != '') {

      //$filter2 = 'year1,year2,year3';
      $filter3 = explode(',', $request->input('year'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $filter3_content .= 'OR year:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_year = ' AND (' . $filter3_content . ') ';
    }
    //end filter year

    $textsearch .= $option_search_year;
    $textsearch .= $option_search_language;

    if ($request->input('title') && $request->input('title') != "") {


      $title =  $this->replace_reserved($request->input('title'));
      $title = "*" . $title . "*";
      if ($request->input('filter4') and $request->input('filter4') != '') {
        if ($request->input('filter4') == 'contains')
          $textsearch .= ' AND +title:' . $title . '';
        if ($request->input('filter4') == 'exactvalue')
          $textsearch .= ' AND title:"' . $title . '"^40';
        if ($request->input('filter4') == 'anyword')
          $textsearch .= ' AND title:' . $title . '';
      } else {
        if ($request->input('typeallrequest') && $request->input('typeallrequest') != '') {
          if ($request->input('typeallrequest') == 'contains')
            $textsearch .= ' AND +title:' . $title . '';
          if ($request->input('typeallrequest') == 'exactvalue')
            $textsearch .= ' AND title:"' . $title . '"^40';
          if ($request->input('typeallrequest') == 'anyword')
            $textsearch .= ' AND title:' . $title . '';
        } else
          $textsearch .= ' AND (title:' . $title . ')';
      }
    }
    if ($request->input('author') && $request->input('author') != "") {

      $author =  $this->replace_reserved($request->input('author'));
      if ($request->input('filterauthor') and $request->input('filterauthor') != '') {
        if ($request->input('filterauthor') == 'contains')
          $textsearch .= ' AND +author:' . $author . '';
        if ($request->input('filterauthor') == 'exactvalue')
          $textsearch .= ' AND author:"' . $author . '"^40';
        if ($request->input('filterauthor') == 'anyword')
          $textsearch .= ' AND author:' . $author . '';
      } else {
        if ($request->input('typeallrequest') && $request->input('typeallrequest') != '') {
          if ($request->input('typeallrequest') == 'contains')
            $textsearch .= ' AND +author:' . $author . '';
          if ($request->input('typeallrequest') == 'exactvalue')
            $textsearch .= ' AND author:"' . $author . '"^40';
          if ($request->input('typeallrequest') == 'anyword')
            $textsearch .= ' AND author:' . $author . '';
        } else
          $textsearch .= ' AND (author:' . $author . ')';
      }
    }
    if ($request->input('second_title_one') && $request->input('second_title_one') != "") {
      $second_title_one =  $this->replace_reserved($request->input('second_title_one'));
      if ($request->input('filter4') and $request->input('filter4') != '') {
        if ($request->input('filter4') == 'contains')
          $textsearch .= ' AND +second_title_one:' . $second_title_one . '';
        if ($request->input('filter4') == 'exactvalue')
          $textsearch .= ' AND second_title_one:"' . $second_title_one . '"^40';
        if ($request->input('filter4') == 'anyword')
          $textsearch .= ' AND second_title_one:' . $second_title_one . '';
      } else {
        if ($request->input('typeallrequest') && $request->input('typeallrequest') != '') {
          if ($request->input('typeallrequest') == 'contains')
            $textsearch .= ' AND +second_title_one:' . $second_title_one . '';
          if ($request->input('typeallrequest') == 'exactvalue')
            $textsearch .= ' AND second_title_one:"' . $second_title_one . '"^40';
          if ($request->input('typeallrequest') == 'anyword')
            $textsearch .= ' AND second_title_one:' . $second_title_one . '';
        } else
          $textsearch .= ' AND (second_title_one:' . $second_title_one . ')';
      }
    }
    if ($request->input('filter5') && $request->input('filter5') != "" && $request->input('page_number') && $request->input('page_number') != "") {
      $page_number =  $this->replace_reserved($request->input('page_number'));
      $page_number = is_numeric($page_number) ? $page_number : false;

      if ($page_number)
        if ($request->input('filter5') && $request->input('filter5') != '') {

          if ($request->input('filter5') == 'less')
            $textsearch .= ' AND page_number:<' . (int)$page_number . '';
          if ($request->input('filter5') == 'equal')
            $textsearch .= ' AND page_number:=' . (int)$page_number . '^40';
          if ($request->input('filter5') == 'more')
            $textsearch .= ' AND page_number:>' . (int)$page_number . '';
        } else {
          $textsearch .= ' AND (page_number:=' . $page_number . ')';
        }
    }
    if ($request->input('sourcepublisher') && $request->input('sourcepublisher') != "") {
      $sourcepublisher =  $this->replace_reserved($request->input('sourcepublisher'));


      if ($sourcepublisher)
        $textsearch .= ' AND (publisher:"' . $sourcepublisher . '"^40 OR author:"' . $sourcepublisher . '"^40  OR university:"' . $sourcepublisher . '"^40  OR country:"' . $sourcepublisher . '"^40 )';
    }
    if ($request->input('titlesearch') && $request->input('titlesearch') != "") {
      if ($request->input('selectsectorhidden') && $request->input('selectsectorhidden') != "") {
        $filterselector = explode(',', $request->input('selectsectorhidden'));
        $filter1 = explode(',', $request->input('filter1'));
        $titleModels = [];
        $textsearchselector = '';
        /*if(!in_array('titleannualinterconference',$filterselector)){
                if (($key = array_search('annual_inter_conferences', $array_search)) !== false) {
                    unset($array_search[$key]);
                }
            }*/
        /*if(!in_array('titleinterconferencerecommendation',$filterselector)){
                if (($key = array_search('inter_conference_recommendations', $array_search)) !== false) {
                    unset($array_search[$key]);
                }
            }*/



        if (in_array('titleabook', $filterselector)) {
          $titleModels[] = (new Book())->searchableAs();
        }
        if (in_array('titleinterstandard', $filterselector)) {
          $titleModels[] =  (new InterStandard())->searchableAs();
        }

        if (in_array('titlejuriprudence', $filterselector)) {
          $titleModels[] = (new Jurisprudence())->searchableAs();
        }
        if (in_array('titlelegalbody', $filterselector)) {
          $titleModels[] =  (new LegalBody())->searchableAs();
        }


        if (in_array('titlemagazine', $filterselector)) {
          $titleModels[] =  (new Magazine())->searchableAs();
        }
        if (in_array('titleregulation', $filterselector)) {
          $titleModels[] =   (new Regulation())->searchableAs();
        }
        if (in_array('titlereport', $filterselector)) {
          $titleModels[] =   (new Report())->searchableAs();
        }


        if (in_array('titleresearch', $filterselector)) {
          $titleModels[] =   (new Research())->searchableAs();
        }

        if (in_array('titleuniversitynewsletter', $filterselector)) {
          $titleModels[] =   (new UniversityNewsletter())->searchableAs();
        }

        if (in_array('titleinterconference', $filterselector)) {
          $titleModels[] =   (new AnnualInterConference())->searchableAs();
          $titleModels[] =   (new InterConference())->searchableAs();
          $titleModels[] =   (new InterConferenceRecommendation())->searchableAs();
        }

        if ($titleModels) {
          if ($array_search) {
            $array_search = array_intersect($array_search, $titleModels);
          } else {
            $array_search = $titleModels;
          }
        }
      }

      $textsearch .= ' AND (' . $request->input('titlesearch') . ' OR "' . $request->input('titlesearch') . '"^50)';
    }
    $textsearch = substr($textsearch, 5);
    if ($textsearch != '') {
      $textsearch = '' . $textsearch . ' AND is_valid:1 ';


      $queryString = $this->generateQuerySearch(
        [
          'search'          => $this->replace_reserved($request->input('titlesearch')),
          'sourcepublisher' => $request->input('sourcepublisher'),
          'titleModel'      => $request->input('selectsectorhidden'),
          'author'          => $request->input('author'),
          'year'            => $request->input('year'),
          'typeRequest'     => $request->input('typeallrequest'),
          'language'        => $request->input('language'),
          'pagesOperator'   => $request->input('filter5'),
          'page_number'     => $request->input('page_number'),

        ],
        $array_search
      );
      $textsearch = $queryString;
      $results = MixedSearch::search(regenerateStemmerSearch($textsearch))->within(implode(',', $array_search))->paginate(10000);
    }
    // $elasticsearchResults = $results->toArray();
    $perPage = 300;
    $total = $results->total();
    $searchingPage = $_GET["pageSearch"];
    $currentPage = $searchingPage;
    //working on items
    $items = $results->items();
    $matchingItems = [];
    $containingItems = [];
    $nonMatchingItems = [];
    $searchWord = $request->input('titlesearch');
    $searchType = $request->input('typeallrequest');
    $cleanedInput = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $searchWord));

    if ($searchType === 'exactvalue') {
      foreach ($items as $result) {
        $title = $result->getTitle();
        $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));

        if ($cleanedTitle == $cleanedInput) {
          $matchingItems[] = $result;
        }
      }
    } elseif ($searchType === 'contains') {
      foreach ($items as $result) {
        $title = $result->getTitle();
        $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));
        if ($cleanedTitle == $cleanedInput) {
          $matchingItems[] = $result;
        } elseif (str_contains($cleanedTitle, $cleanedInput)) {
          $containingItems[] = $result;
        }
      }
    } else {
      foreach ($items as $result) {
        $title = $result->getTitle();
        $author = $result["author"];
        $publisher = $result["publisher"];
        $university = $result["university"];
        $country = $result["country"];
        $secondaryTitleOne = $result["secondary_title_one"];

        $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));
        if ($cleanedTitle == $cleanedInput || $secondaryTitleOne == $searchWord || $author == $searchWord || $publisher == $searchWord) {
          if (isset($sourcepublisher)) {
            if ($sourcepublisher == $author || $sourcepublisher == $publisher || $sourcepublisher == $university || $sourcepublisher == $country) {
              $matchingItems[] = $result;
            }
          } else {
            $matchingItems[] = $result;
          }
        } elseif (str_contains($cleanedTitle, $cleanedInput) || str_contains($secondaryTitleOne, $searchWord) || str_contains($author, $searchWord) || str_contains($publisher, $searchWord)) {
          if (isset($sourcepublisher)) {
            if (str_contains($author, $sourcepublisher) || str_contains($publisher, $sourcepublisher) || str_contains($university, $sourcepublisher) || str_contains($country, $sourcepublisher)) {
              $containingItems[] = $result;
            }
          } else {
            $containingItems[] = $result;
          }
        } else {
          $nonMatchingItems[] = $result;
        }
      }
    }
    $items = array_merge($matchingItems, $containingItems, $nonMatchingItems);
    $total = count($items);
    $items = array_slice($items, ($searchingPage - 1) * $perPage, $perPage);

    $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
      $items,
      $total,
      $perPage,
      $currentPage,
      ['path' => request()->url(), 'query' => request()->query()]
    );
    $results = $paginatedResults;

    $languages = Language::all();
    $fields['title'] = $request->input('title');
    $fields['titlesearch'] = $request->input('titlesearch');
    $fields['sourcepublisher'] = $request->input('sourcepublisher');
    $fields['author'] = $request->input('author');
    $fields['second_title_one'] = $request->input('second_title_one');
    $fields['page_number'] = $request->input('page_number');



    if ($results) {
      $view = view('fetchadvancedsearch', [
        'results' => $results,
        'models' => $models,
        'fields' => $fields,
        'filter1' => $request->input('filter1'),
        'selectsectorhidden' => $request->input('selectsectorhidden'),
        'languages' => $languages,
        'language' => $request->input('language'),
        'year' => $request->input('year'),
        'filter4' => $request->input('filter4'),
        'filter5' => $request->input('filter5'),
        'page_number' => is_numeric($request->input('page_number')), //;//($request->input('page_number')) ? '' :$request->input('page_number') ,
        'filterauthor' => $request->input('filterauthor'),
        'typeallrequest' => $request->input('typeallrequest'),
        'search' => ''

      ])->render();
      return response()->json(['view' => $view, 'results' => $results]);
    } else {
      return '';
    }
  }

  public function getproprieties($results)
  {
    $exporters = [];
    $years_release = [];
    $authors = [];

    foreach ($results as $model_result) {
      if (!in_array($model_result->getexporter(), $exporters, true)) {
        if ($model_result->getexporter() != '')
          array_push($exporters, $model_result->getexporter());
      }

      if (!in_array($model_result->year, $years_release, true)) {
        if ($model_result->year != '')
          array_push($years_release, $model_result->year);
      }

      if (!in_array($model_result->author, $authors, true)) {
        if ($model_result->author != '')
          array_push($authors, $model_result->author);
      }
    }

    $proprities['exporters'] = $exporters;
    $proprities['years_release'] = $years_release;
    $proprities['authors'] = $authors;
    return $proprities;
  }







  public function trucatefinancedata()
  {
    Artisan::call("financedata:truncate");
  }
  public function validateall()
  {
    Artisan::call("validateall:all");
  }
  public function unvalidateall()
  {
    Artisan::call("unvalidateall:all");
  }


  public function countrecordmodel($id)
  {
    $recordedmodel = Modelgroup::where('class', $id)->first();
    $recordedmodel =  '\App\Models\\' . $recordedmodel->name;

    $records =   $recordedmodel::count();
    return $records;
  }


  public function generateQuerySearch($entries, $array_search)
  {
    $searchKeys       = $entries['search'] ?? "";
    $sourcepublisher  = $entries['sourcepublisher'] ?? "";
    $titleModel       = $entries['titleModel'] ?? "";
    $year             = $entries['year'] ?? "";
    $author           = $entries['author'] ?? "";
    $typeRequest      = $entries['typeRequest'] ?? "";
    $language         = $entries['language'] ?? "";
    $pagesOperator    = $entries['pagesOperator'] ?? "";
    $page_number      = $entries['page_number'] ?? "";



    /*
      if($typeRequest=='contains')
      $textsearch .= ' AND +title:'.$title.'';
      if($typeRequest=='exactvalue')
      $textsearch .= ' AND title:"'.$title.'"^40';
      if($typeRequest=='anyword')
      $textsearch .= ' AND title:'.$title.'';

*/

    $query = '(*' . $searchKeys . '* OR "*' . $searchKeys . '*"^40 OR ( title:(*' . $searchKeys . '*)^70 OR author:(*' . $searchKeys . '*)^7 secondary_title_one:(*' . $searchKeys . '*)^5)) AND is_valid:1';
    if ($typeRequest == 'contains')
      $query = '(*' . $searchKeys . '* OR "*' . $searchKeys . '*"^40 OR ( title:(*' . $searchKeys . '*)^70 OR author:(*' . $searchKeys . '*)^7 secondary_title_one:(*' . $searchKeys . '*)^5)) AND is_valid:1';

    if ($typeRequest == 'exactvalue')
      $query = '("' . $searchKeys . '"^40 OR ( title:("' . $searchKeys . '")^70 OR author:("' . $searchKeys . '")^7 secondary_title_one:("' . $searchKeys . '")^5)) AND is_valid:1';

    if ($typeRequest == 'anyword')
      $query = '(' . $searchKeys . ' OR "' . $searchKeys . '"^40 OR ( title:(' . $searchKeys . ')^70 OR author:(' . $searchKeys . ')^7 secondary_title_one:(' . $searchKeys . ')^5)) AND is_valid:1';



    if ($titleModel) {

      $query = '(title:(*' . $searchKeys . '*)^70 ) AND is_valid:1';

      if ($typeRequest == 'contains')
        $query = '(title:(*' . $searchKeys . '*)^70 ) AND is_valid:1';

      if ($typeRequest == 'exactvalue')
        $query = '(title:("' . $searchKeys . '")^70 ) AND is_valid:1';

      if ($typeRequest == 'anyword')
        $query = '(title:(' . $searchKeys . ')^70 ) AND is_valid:1';
    }


    if ($sourcepublisher) {
      $query .= ' AND (publisher:(*' . $sourcepublisher . '*)^40 OR author:(*' . $sourcepublisher . '*)^40  OR university:(*' . $sourcepublisher . '*)^40  OR country:(*' . $sourcepublisher . '*)^40 )';

      if ($typeRequest == 'contains')
        $query .= ' AND (publisher:(*' . $sourcepublisher . '*)^40 OR author:(*' . $sourcepublisher . '*)^40  OR university:(*' . $sourcepublisher . '*)^40  OR country:(*' . $sourcepublisher . '*)^40 )';

      if ($typeRequest == 'exactvalue')
        $query .= ' AND (publisher:"' . $sourcepublisher . '"^40 OR author:"' . $sourcepublisher . '"^40  OR university:"' . $sourcepublisher . '"^40  OR country:"' . $sourcepublisher . '"^40 )';

      if ($typeRequest == 'anyword')
        $query .= ' AND (publisher:' . $sourcepublisher . '^40 OR author:' . $sourcepublisher . '^40  OR university:' . $sourcepublisher . '^40  OR country:' . $sourcepublisher . '^40 )';
    }


    if ($year) {

      $query .= ' AND year:' . $year;

      if ($typeRequest == 'contains')
        $query .= ' AND year:(*' . $year . '*)';
      if ($typeRequest == 'exactvalue')
        $query .= ' AND year:"' . $year . '"';
      if ($typeRequest == 'anyword')
        $query .= ' AND year:' . $year;
    }


    if ($author) {
      $query .= ' AND author:(*' . $author . '*)';
      if ($typeRequest == 'contains')
        $query .= ' AND author:(*' . $author . '*)';
      if ($typeRequest == 'exactvalue')
        $query .= ' AND author:"' . $author . '"';
      if ($typeRequest == 'anyword')
        $query .= ' AND author:(' . $author . ')';
    }



    if ($page_number) {
      if ($pagesOperator) {
        if ($pagesOperator == 'less') {
          $query .= ' AND page_number:<' . (int)$page_number . '';
        } elseif ($pagesOperator == 'more') {
          $query .= ' AND page_number:>' . (int)$page_number . '';
        } else {
          $query .= ' AND page_number:' . $page_number . '';
        }
      } else {
        $query .= ' AND page_number:' . $page_number . '';
      }
    }


    if ($language) {
      $langs = explode(',', $language);
      $filter_language = '';
      if ($langs && sizeof($langs) < 3) {
        foreach ($langs as $lang) {

          $filter_language .= 'OR language_id:' . (int)$lang . ' ';
        }
        $filter_language = substr($filter_language, 2);
        $filter_language = substr($filter_language, 1, -1);
        $query .= ' AND (' . $filter_language . ') ';
      }
    }

    return $query;
  }
}
