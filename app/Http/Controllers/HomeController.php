<?php

namespace App\Http\Controllers;

use App;
use App\Models\AnnualInterConference;
use App\Models\Book;
use App\Models\Contact;
use App\Models\Filesource;
use App\Models\Indice;
use App\Models\InterConference;
use App\Models\InterConferenceRecommendation;
use App\Models\InterStandard;
use App\Models\Jurisprudence;
use App\Models\Language;
use App\Models\LegalBody;
use App\Models\Magazine;
use App\Models\Modelgroup;
use App\Models\Regulation;
use App\Models\Report;
use App\Models\Research;
use App\Models\Sourcegroup;
use App\Models\UniversityNewsletter;
use App\Http\Controllers\SearchFields;
use Elasticsearch\ClientBuilder;
use Matchish\ScoutElasticSearch\MixedSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Matchish\ScoutElasticSearch\Engines\ElasticSearchEngine;
use Redirect;
use stdClass;
use Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use romanzipp\Seo\Structs\Meta;



use ONGR\ElasticsearchDSL\Query\MatchAllQuery;

use ONGR\ElasticsearchDSL\Search;

class HomeController extends Controller
{

  public function index(Request $request)
  {
    $results = [];

    $array_search = [
      (new AnnualInterConference())->searchableAs(),
      (new Book())->searchableAs(),
      (new InterConference())->searchableAs(),
      (new InterConferenceRecommendation())->searchableAs(),
      (new Magazine())->searchableAs(),
      (new Research())->searchableAs(),
      (new UniversityNewsletter())->searchableAs(),
      (new Regulation())->searchableAs(),
      (new Report())->searchableAs(),
      (new InterStandard())->searchableAs(),
      (new Jurisprudence())->searchableAs(),
      (new LegalBody())->searchableAs(),
    ];



    function containsArabicLetters($words, $arabicLetters)
    {
      $wordsUnicode = preg_replace('//u', '', $words);
      $arabicLettersUnicode = array_map(function ($letter) {
        return mb_convert_encoding($letter, 'UTF-8', 'UTF-8');
      }, $arabicLetters);

      foreach ($arabicLettersUnicode as $letter) {
        if (mb_strpos($wordsUnicode, $letter) !== false) {
          return true;
        }
      }

      return false;
    }

    //filter models
    if ($request->input('filter1')) {

      $filter1 = explode(',', $request->input('filter1'));
      $filtermodels = '';
      foreach ($filter1 as $element) {
        if (in_array($element, ['AnnualInterConference', 'Book', 'InterConference', 'InterConferenceRecommendation', 'InterStandard', 'Jurisprudence', 'LegalBody', 'Magazine', 'Regulation', 'Report', 'Research', 'UniversityNewsletter'])) {
          $selctedsearchmodel = (new ('App\Models\\' . $element)())->searchableAs();
          if (in_array($selctedsearchmodel, $array_search, true)) {
            $element = 'App\Models\\' . $element;
            $filtermodels .= ',' . (new $element())->searchableAs();
          }
        } else redirect()->route('index.' . app()->getLocale());
      }
      if ($filtermodels != '') {
        $array_search = explode(',', $filtermodels);
        $array_search = array_filter($array_search);
      }
    }
    //end filter models

    $option_search = '';
    //filter authors
    $option_search_author = '';
    if ($request->input('filter2') && $request->input('filter2') != '') {

      //$filter2 = 'model1,model2,model3';
      $filter2 = explode(',', $request->input('filter2'));
      $filter2_content = '';
      foreach ($filter2 as $element) {
        $element = $this->replace_reserved($element);
        $filter2_content .= ' OR author:"' . $element . '"';
      }
      $filter2_content = substr($filter2_content, 3);
      $option_search_author = ' AND (  ' . $filter2_content . ') ';
    }
    //end filter authors

    //filter year
    $option_search_year = '';
    if ($request->input('year') && $request->input('year') != '') {

      //$filter2 = 'year1,year2,year3';
      $filter3 = explode(',', $request->input('year'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $element = $this->replace_reserved($element);
        $filter3_content .= 'OR year:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_year = ' AND (' . $filter3_content . ') ';
    }
    //end filter year


    //filter language
    $option_search_language = '';
    if ($request->input('language') && $request->input('language') != '') {

      //$filter2 = 'year1,year2,year3';

      $filter3 = explode(',', $request->input('language'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $element = $this->replace_reserved($element);
        $filter3_content .= 'OR language_id:' . (int)$element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_language = ' AND (' . $filter3_content . ') ';
    }
    //end filter language

    //filter type
    $option_search_type = '';
    if ($request->input('types') && $request->input('types') != '') {
      //$filter2 = 'year1,year2,year3';

      $filter3 = explode(',', $request->input('types'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $element = $this->replace_reserved($element);
        if ($element != '')
          $filter3_content .= 'OR type:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      if ($filter3_content != '')
        $option_search_type = ' AND (' . $filter3_content . ') ';
    }
    //end filter type

    //filter subjects
    $option_search_selected_subject = '';
    if ($request->input('selectedsubject') && $request->input('selectedsubject') != '') {
      //$filter2 = 'year1,year2,year3';

      $filtersubject = explode(',', $request->input('selectedsubject'));
      $filtersubject_content = '';
      foreach ($filtersubject as $element) {

        $element = $this->replace_reserved($element);
        if ($element != '')
          $filtersubject_content .= 'OR sourcegroup_id:"' . $element . '" ';
      }
      $filtersubject_content = substr($filtersubject_content, 2);
      $filtersubject_content = substr($filtersubject_content, 1, -1);
      if ($filtersubject_content != '')
        $option_search_selected_subject = ' AND (' . $filtersubject_content . ') ';
    }
    //end filter subjects




    $option_search = $option_search_author . $option_search_year . $option_search_language . $option_search_type . $option_search_selected_subject;
    if (($request->input('search') && strlen(utf8_decode($request->input('search'))) > 2) || $request->input('language') || $request->input('year') || $request->input('author')) {

      $option_search .= " AND (is_valid:1)";
      $arabicLetters = [
        'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
        'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
      ];

      $search = $this->replace_reserved($request->input('search'));
      $words = explode(" ", $search);
      $modifiedWords = array_map(function ($word) use ($arabicLetters) {
        if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetters($word, $arabicLetters)) {
          $word = "ال" . $word;
        }
        return $word;
      }, $words);
      if (count($words) == 1) {
        $search = implode(" ", $modifiedWords);
      }
      $searchWord = $this->replace_reserved($request->input('search'));
      if ($searchWord == "على") {
        $search = "result_should_be_null";
      }
      if ($search != '') {
        // $shortVowels = array('َ', 'ُ', 'ِ', 'ْ', 'ّ');
        // $search = str_replace($shortVowels, '', $search);
        $searchlike =  "*" . $search . "*";
        $searchlike = preg_replace('/\b\w{1,2}\b\s*/u', '', $search);

        $searchWeights = config('search_weights');
        $scoresearch = '(' . $search . ' OR "' . $searchlike . '" OR (';
        foreach ($searchWeights as $field => $weight) {
          $scoresearch .= "{$field}:(\"{$search}\")^{$weight} OR ";
        }
        $scoresearch = rtrim($scoresearch, ' OR ') . ')) ';
      } else {
        $scoresearch = '';
        $option_search = substr($option_search, 4);
      }
      $query_search = $scoresearch . $option_search;
      $results = MixedSearch::search(regenerateStemmerSearch($query_search))
        ->within(implode(',', $array_search))
        ->paginate(10000);
    }
    $models = Modelgroup::all()->sortBy('screenorder');
    $languages = Language::all();



    $subjects = Sourcegroup::all();


    if ($results) {
      ///$results->items()->pluck('sourcegroup_id')->toArray();
      function fetchHijriToGregorian($hijriDate)
      {
        $response = Http::get("http://api.aladhan.com/v1/hToG/{$hijriDate}");

        if ($response->successful()) {
          $data = $response->json();
          return $data;
        } else {
          // Handle the error case here
          return [];
        }
      }

      $subjects = Sourcegroup::whereIn('id', array_unique(array_column($results->items(), 'sourcegroup_id')))->get();
      $resultauthor = [];
      foreach ($results as $result) {
        if (!in_array($result->author, $resultauthor, true)) {
          if ($result->author != '')
            array_push($resultauthor, $result->author);
        }
      }
      $resultyears = [];
      foreach ($results as $result) {
        $year = $result->year;
        if (str_contains($year, "ه")) {
          // $cleanedyear = str_replace("ه", "", $year);
          $cleanedyear = preg_replace('/[^\d\/ ]+/u', '', $year);
          $cleanedyear = trim($cleanedyear);
          $cleanedyear = substr($cleanedyear, 0, 4);
          // $myyear=fetchHijriToGregorian("01-01-".$cleanedyear);
          // if($myyear)
          // {
          //   $newyear = substr($myyear["data"]["gregorian"]["date"], 6, 4);
          //   $year=$newyear;
          // }
        } elseif (str_contains($year, "م")) {
          $cleanedyear = preg_replace('/[^\d\/ ]+/u', '', $year);
          $cleanedyear = trim($cleanedyear);
          $year = $cleanedyear;
        }
        if (!in_array($year, $resultyears, true)) {
          if ($year != '') {
            array_push($resultyears, $year);
          }
        }
      }

      rsort($resultyears);
      function clean($word)
      {
        $cleaned = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $word));
        return $cleaned;
      }

      $searchWord = $_GET["search"];
      $cleanedInput = clean($searchWord);
      // $elasticsearchResults = $results->toArray();
      $perPage = 300;
      $total = $results->total();
      $currentPage = $results->currentPage();
      $items = $results->items();
      $matchingItems = [];
      $containingItems = [];
      $nonMatchingItems = [];


      foreach ($items as $result) {
        $item = $result->getAttributes();
        $title = $result->getTitle();

        // $year=$result['year'];
        // if (str_contains($year,"ه")) {
        //   $cleanedyear = preg_replace('/[^\d\/ ]+/u', '', $year);
        //   $cleanedyear = trim($cleanedyear);
        //   $cleanedyear = substr($cleanedyear, 0, 4);
        //   $myyear=fetchHijriToGregorian("01-01-".$cleanedyear);
        //   if($myyear)
        //   {
        //     $newyear = substr($myyear["data"]["gregorian"]["date"], 6, 4);
        //     $year=$newyear;
        //   }
        // } elseif(str_contains($year,"م")){
        //   $cleanedyear = str_replace("م", "", $year);
        //   $cleanedyear = trim($cleanedyear);
        //   $year=$cleanedyear;
        // }
        // $result['year']=$year;
        $cleanedTitle = clean($title);
        if ($cleanedTitle == $cleanedInput) {
          $matchingItems[] = $result;
        } elseif (str_contains($cleanedTitle, $cleanedInput)) {
          $containingItems[] = $result;
        } else {
          $nonMatchingItems[] = $result;
        }
      }
      $items = array_merge($matchingItems, $containingItems, $nonMatchingItems);


      $total = count($items);
      $items = array_slice($items, ($currentPage - 1) * $perPage, $currentPage * $perPage);


      $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
        $items,
        $total,
        $perPage,
        $currentPage,
        ['path' => request()->url(), 'query' => request()->query()]
      );
      $results = $paginatedResults;
      //dd($resultyears);
      return view(
        'filterresults',
        [
          'results' => $results,
          'search' => $request->input('search'),
          'models' => $models,
          'filter1' => $request->input('filter1'),
          'filter2' => $request->input('filter2'),
          'year' => $request->input('year'),
          'yearsrelease' => $resultyears,
          'language' => $request->input('language'),
          'types2' => $request->input('types'),
          'selectedsubject'   => $request->input('selectedsubject'),
          'subjects' => $subjects,
          'authors' => $resultauthor,
          'languages' => $languages,

        ]
      );
    } else
      return view('index', ['models' => $models]);
  }

  public function search(Request $request, $model)
  {

    $results = [];
    $proprities = [];

    $models = Modelgroup::orderBy('screenorder', 'asc')->get();


    $model = Modelgroup::where('slug', $model)->first();
    if (!$model)
      return abort('404');
    if ($model) {
      $importmodel = 'App\Models\\' . $model->name;

      $model = $model->name;


      $array_search = [(new $importmodel())->searchableAs()];
    } else {
      $array_search = [
        (new AnnualInterConference())->searchableAs(),
        (new Book())->searchableAs(),
        (new InterConference())->searchableAs(),
        (new InterConferenceRecommendation())->searchableAs(),
        (new Magazine())->searchableAs(),
        (new Research())->searchableAs(),
        (new UniversityNewsletter())->searchableAs(),
        (new Regulation())->searchableAs(),
        (new Report())->searchableAs(),
        (new InterStandard())->searchableAs(),
        (new Jurisprudence())->searchableAs(),
        (new LegalBody())->searchableAs(),
      ];
    }
    $search = '';

    $search =  $request->input('search');
    function containsArabicLetter($words, $arabicLetters)
    {
      $wordsUnicode = preg_replace('//u', '', $words);
      $arabicLettersUnicode = array_map(function ($letter) {
        return mb_convert_encoding($letter, 'UTF-8', 'UTF-8');
      }, $arabicLetters);

      foreach ($arabicLettersUnicode as $letter) {
        if (mb_strpos($wordsUnicode, $letter) !== false) {
          return true;
        }
      }

      return false;
    }
    if ($search) {

      $words = explode(" ", $search);

      $arabicLetters = [
        'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
        'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
      ];
      $modifiedWords = array_map(function ($word) use ($arabicLetters) {
        if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetter($word, $arabicLetters)) {
          $word = "ال" . $word;
        }
        return $word;
      }, $words);
      if (count($words) == 1) {
        $search = implode(" ", $modifiedWords);
      }
      $searchWord = $this->replace_reserved($request->input('search'));
      if ($searchWord == "على") {
        $search = "result_should_be_null";
      }
    }

    $search = $this->replace_reserved($search);
    //check search filters
    if ($search || $request->get('exporter') || $request->get('year') || $request->get('author') || $request->get('language')) {



      $search_author = '';
      if ($request->get('author') and $request->get('author') != '')
        $search_author = ' AND author:"' . $request->get('author') . '"^5';

      $search_year = '';
      if ($request->get('year') and $request->get('year') != '')
        $search_year = ' AND year:"' . $request->get('year') . '"';

      $search_language = '';
      if ($request->get('language') and $request->get('language'))
        $search_language = ' AND language_id:"' . $request->get('language') . '"';

      $search_exporter = '';
      if ($request->get('exporter') and $request->get('exporter') != '') {

        if ($model == 'Regulation')
          $search_exporter = ' AND country:"' . $request->get('exporter') . '"';

        elseif ($model == 'UniversityNewsletter')
          $search_exporter = ' AND university:"' . $request->get('exporter') . '"';
        else
          $search_exporter = ' AND author:"' . $request->get('exporter') . '"';
      }


      $filtersearch  = $search_author . $search_exporter . $search_year . $search_language;
      $filtersearch .= ' AND is_valid:1';

      if ($search == '') {

        $search = substr($filtersearch, 5);
      } else {





        $searchlike =  "*" . $search . "*";
        $searchlike = preg_replace('/\b\w{1,2}\b\s*/u', '', $search);


        $score = ' ( (' . $search . ') OR

                ("' . $searchlike . '")^100 OR

                title:("' . $search . '")^150 OR
                author:("' . $search . '")^140 OR
                main_title:("' . $search . '")^100 OR
                secondary_title_one:("' . $search . '")^100 OR
                secondary_title_two:("' . $search . '")^100 OR

                organiser:("' . $search . '")^70 OR
                conference_title:("' . $search . '")^70 OR
                session_title:("' . $search . '")^70 OR
                chapter_title:("' . $search . '")^70 OR
                search_title:("' . $search . '")^70 OR
                request_title:("' . $search . '")^70 OR
                publisher:("' . $search . '")^70 OR
                act_title:("' . $search . '")^70 OR
                request_title:("' . $search . '")^70 OR
                publisher:("' . $search . '")^70

                )';

        $trimsearch = preg_replace('/\s+/', '', $search);
        if ($filtersearch != '') {
          $search = '(' . $score . '^200 OR ' . $trimsearch . '^1 OR "' . $search . '"^1 ) AND (' . substr($filtersearch, 5) . ')';
        }
      }

      $results = MixedSearch::search(regenerateStemmerSearch($search));
      $results = $results->within(implode(',', $array_search))->paginate(10000);
      function clean($word)
      {
        $cleaned = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $word));
        return $cleaned;
      }
      $searchWord = $_GET["search"];
      $cleanedInput = clean($searchWord);
      // $elasticsearchResults = $results->toArray();
      $perPage = 300;
      $total = $results->total();
      $currentPage = $results->currentPage();
      $items = $results->items();
      $matchingItems = [];
      $containingItems = [];
      $nonMatchingItems = [];
      foreach ($items as $result) {
        $item = $result->getAttributes();
        $title = $result->getTitle();

        $cleanedTitle = clean($title);
        if ($cleanedTitle == $cleanedInput) {
          $matchingItems[] = $result;
        } elseif (str_contains($cleanedTitle, $cleanedInput)) {
          $containingItems[] = $result;
        } else {
          $nonMatchingItems[] = $result;
        }
      }
      $items = array_merge($matchingItems, $containingItems, $nonMatchingItems);
      $total = count($items);
      $items = array_slice($items, ($currentPage - 1) * $perPage, $currentPage * $perPage);
      $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
        $items,
        $total,
        $perPage,
        $currentPage,
        ['path' => request()->url(), 'query' => request()->query()]
      );
      $results = $paginatedResults;

      //count same search filtred results in the other models
      foreach ($models as $modeltype) {
        $importmodelty = 'App\Models\\' . $modeltype->name;
        $arraycount = [(new $importmodelty())->searchableAs(),];
        $model_results[$modeltype->name] = MixedSearch::search(regenerateStemmerSearch($search));
        $model_results[$modeltype->name] = $model_results[$modeltype->name]->within(implode(',', $arraycount))->paginate(25)->total();
      }
      //get filtrs ( author exporter year)
      $proprities = $this->getproprieties($results);
    } else {

      if ($model) {
        $importmodel = 'App\Models\\' . $model;
        $results = $importmodel::where('is_valid', 1)->paginate(25);
      }
      //Modelname::where('status',1)->paginate(25);

      $model_results['AnnualInterConference'] = AnnualInterConference::where('is_valid', 1)->count('id');
      $model_results['Book'] = Book::where('is_valid', 1)->count('id');
      $model_results['InterConference'] = InterConference::where('is_valid', 1)->count('id');
      $model_results['InterConferenceRecommendation'] = InterConferenceRecommendation::where('is_valid', 1)->count('id');
      $model_results['InterStandard'] = InterStandard::where('is_valid', 1)->count('id');
      $model_results['Jurisprudence'] = Jurisprudence::where('is_valid', 1)->count('id');
      $model_results['LegalBody'] = LegalBody::where('is_valid', 1)->count('id');
      $model_results['Magazine'] = Magazine::where('is_valid', 1)->count('id');
      $model_results['Regulation'] = Regulation::where('is_valid', 1)->count('id');
      $model_results['Report'] = Report::where('is_valid', 1)->count('id');
      $model_results['Research'] = Research::where('is_valid', 1)->count('id');
      $model_results['UniversityNewsletter'] = UniversityNewsletter::where('is_valid', 1)->count('id');

      if ($results)
        $proprities = $this->getproprieties($results);
    }


    foreach ($models as $modelitem) {
      if ($modelitem->name == $model)
        $model = $modelitem->title;
    }
    $languages = Language::all();

    $models = Modelgroup::all()->sortBy('screenorder');

    if (($request->input('search') && strlen(utf8_decode($request->input('search'))) > 2) || $request->get('exporter') || $request->get('year') || $request->get('author') || $request->get('language')) {
      return view(
        'result',
        [
          'results' => $results,
          'model_results' => $model_results,
          'model' => $model,
          'models' => $models,
          'search' => $request->input('search'),
          'proprities' => $proprities,
          'langauges' => $languages
        ]
      );
    } else {
      if (app()->getLocale() == 'ar')
        $groupslug = $request->segment(3);
      else
        $groupslug = $request->segment(2);

      $modelindexed = Modelgroup::where('slug', '=', $groupslug)->first();

      $indice = Indice::where('class', '=', $modelindexed->class)->with('indicedetails', function ($query) {
        $query->orderby('number');
      })->first();
      return view(
        'aboutgroups.' . $groupslug,
        [
          'model' => $model,
          'indice' => $indice
        ]
      );
    }
  }


  /**
   * Search resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   *
   */
  public function advancedsearch(Request $request)
  {
    $textsearch = "";

    $modelsOptions = [
      "InterStandard"                   => "مجموعة المعيار الدولية",
      "Jurisprudence"                   => "مجموعة قرارات وتوصيات المجامع الفقهية",
      "InterConferenceRecommendation"   => "مجموعة قرارات وتوصيات المؤتمرات الدولية",
      "LegalBody"                       => "مجموعة قرارات وفتاوى الهيئات الشرعية",
      "AnnualInterConference"           => "مجموعة المؤتمرات الدولية الدورية",
      "InterConference"                 => "مجموعة المؤتمرات الدولية",
      "Book"                            => "مجموعة الكتب",
      "UniversityNewsletter"            => "مجموعة الرسائل الجامعية",
      "Magazine"                        => "مجموعة المجلات",
      "Research"                        => "مجموعة الأبحاث",
      "Regulation"                      => "مجموعةالقوانين والتشريعات",
      "Report"                          => "مجموعة التقارير المتعلقة بالصناعة المالية الإسلامية"
    ];



    $models = Modelgroup::all();

    $results = [];
    $array_search = [
      (new AnnualInterConference())->searchableAs(),
      (new Book())->searchableAs(),
      (new InterConference())->searchableAs(),
      (new InterConferenceRecommendation())->searchableAs(),
      (new Magazine())->searchableAs(),
      (new Research())->searchableAs(),
      (new UniversityNewsletter())->searchableAs(),
      (new Regulation())->searchableAs(),
      (new Report())->searchableAs(),
      (new InterStandard())->searchableAs(),
      (new Jurisprudence())->searchableAs(),
      (new LegalBody())->searchableAs(),
    ];
    //filter models
    if ($request->input('filter1')) {
      $filter1 = explode(',', $request->input('filter1'));
      $filtermodels = '';
      foreach ($filter1 as $element) {
        if (in_array($element, ['AnnualInterConference', 'Book', 'InterConference', 'InterConferenceRecommendation', 'InterStandard', 'Jurisprudence', 'LegalBody', 'Magazine', 'Regulation', 'Report', 'Research', 'UniversityNewsletter'])) {
          $selctedsearchmodel = (new ('App\Models\\' . $element)())->searchableAs();
          if (in_array($selctedsearchmodel, $array_search, true)) {
            $element = 'App\Models\\' . $element;
            $filtermodels .= ',' . (new $element())->searchableAs();
          }
        } else redirect()->route('index.' . app()->getLocale());
      }
      if ($filtermodels != '') {
        $array_search = explode(',', $filtermodels);
        $array_search = array_filter($array_search);
      }
    }
    //end filter models


    //filter language
    $option_search_language = '';
    if ($request->input('language') && $request->input('language') != '') {

      $filter3 = explode(',', $request->input('language'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $filter3_content .= 'OR language_id:' . (int)$element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_language = ' AND (' . $filter3_content . ') ';
    }
    //end filter language
    //filter year
    $option_search_year = '';
    if ($request->input('year') && $request->input('year') != '') {

      //$filter2 = 'year1,year2,year3';
      $filter3 = explode(',', $request->input('year'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $filter3_content .= 'OR year:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_year = ' AND (' . $filter3_content . ') ';
    }
    //end filter year

    $textsearch .= $option_search_year;
    $textsearch .= $option_search_language;

    if ($request->input('title') && $request->input('title') != "") {


      $title =  $this->replace_reserved($request->input('title'));
      $title = "*" . $title . "*";
      if ($request->input('filter4') and $request->input('filter4') != '') {
        if ($request->input('filter4') == 'contains')
          $textsearch .= ' AND +title:' . $title . '';
        if ($request->input('filter4') == 'exactvalue')
          $textsearch .= ' AND title:"' . $title . '"^40';
        if ($request->input('filter4') == 'anyword')
          $textsearch .= ' AND title:' . $title . '';
      } else {
        if ($request->input('typeallrequest') && $request->input('typeallrequest') != '') {
          if ($request->input('typeallrequest') == 'contains')
            $textsearch .= ' AND +title:' . $title . '';
          if ($request->input('typeallrequest') == 'exactvalue')
            $textsearch .= ' AND title:"' . $title . '"^40';
          if ($request->input('typeallrequest') == 'anyword')
            $textsearch .= ' AND title:' . $title . '';
        } else
          $textsearch .= ' AND (title:' . $title . ')';
      }
    }
    if ($request->input('author') && $request->input('author') != "") {

      $author =  $this->replace_reserved($request->input('author'));
      if ($request->input('filterauthor') and $request->input('filterauthor') != '') {
        if ($request->input('filterauthor') == 'contains')
          $textsearch .= ' AND +author:' . $author . '';
        if ($request->input('filterauthor') == 'exactvalue')
          $textsearch .= ' AND author:"' . $author . '"^40';
        if ($request->input('filterauthor') == 'anyword')
          $textsearch .= ' AND author:' . $author . '';
      } else {
        if ($request->input('typeallrequest') && $request->input('typeallrequest') != '') {
          if ($request->input('typeallrequest') == 'contains')
            $textsearch .= ' AND +author:' . $author . '';
          if ($request->input('typeallrequest') == 'exactvalue')
            $textsearch .= ' AND author:"' . $author . '"^40';
          if ($request->input('typeallrequest') == 'anyword')
            $textsearch .= ' AND author:' . $author . '';
        } else
          $textsearch .= ' AND (author:' . $author . ')';
      }
    }
    if ($request->input('second_title_one') && $request->input('second_title_one') != "") {
      $second_title_one =  $this->replace_reserved($request->input('second_title_one'));
      if ($request->input('filter4') and $request->input('filter4') != '') {
        if ($request->input('filter4') == 'contains')
          $textsearch .= ' AND +second_title_one:' . $second_title_one . '';
        if ($request->input('filter4') == 'exactvalue')
          $textsearch .= ' AND second_title_one:"' . $second_title_one . '"^40';
        if ($request->input('filter4') == 'anyword')
          $textsearch .= ' AND second_title_one:' . $second_title_one . '';
      } else {
        if ($request->input('typeallrequest') && $request->input('typeallrequest') != '') {
          if ($request->input('typeallrequest') == 'contains')
            $textsearch .= ' AND +second_title_one:' . $second_title_one . '';
          if ($request->input('typeallrequest') == 'exactvalue')
            $textsearch .= ' AND second_title_one:"' . $second_title_one . '"^40';
          if ($request->input('typeallrequest') == 'anyword')
            $textsearch .= ' AND second_title_one:' . $second_title_one . '';
        } else
          $textsearch .= ' AND (second_title_one:' . $second_title_one . ')';
      }
    }
    if ($request->input('filter5') && $request->input('filter5') != "" && $request->input('page_number') && $request->input('page_number') != "") {
      $page_number =  $this->replace_reserved($request->input('page_number'));
      $page_number = is_numeric($page_number) ? $page_number : false;

      if ($page_number)
        if ($request->input('filter5') && $request->input('filter5') != '') {

          if ($request->input('filter5') == 'less')
            $textsearch .= ' AND page_number:<' . (int)$page_number . '';
          if ($request->input('filter5') == 'equal')
            $textsearch .= ' AND page_number:=' . (int)$page_number . '^40';
          if ($request->input('filter5') == 'more')
            $textsearch .= ' AND page_number:>' . (int)$page_number . '';
        } else {
          $textsearch .= ' AND (page_number:=' . $page_number . ')';
        }
    }
    if ($request->input('sourcepublisher') && $request->input('sourcepublisher') != "") {
      $sourcepublisher =  $this->replace_reserved($request->input('sourcepublisher'));


      if ($sourcepublisher)
        $textsearch .= ' AND (publisher:"' . $sourcepublisher . '"^40 OR author:"' . $sourcepublisher . '"^40  OR university:"' . $sourcepublisher . '"^40  OR country:"' . $sourcepublisher . '"^40 )';
    }
    if ($request->input('titlesearch') && $request->input('titlesearch') != "") {
      if ($request->input('selectsectorhidden') && $request->input('selectsectorhidden') != "") {
        $filterselector = explode(',', $request->input('selectsectorhidden'));
        $filter1 = explode(',', $request->input('filter1'));
        $titleModels = [];
        $textsearchselector = '';
        /*if(!in_array('titleannualinterconference',$filterselector)){
                if (($key = array_search('annual_inter_conferences', $array_search)) !== false) {
                    unset($array_search[$key]);
                }
            }*/
        /*if(!in_array('titleinterconferencerecommendation',$filterselector)){
                if (($key = array_search('inter_conference_recommendations', $array_search)) !== false) {
                    unset($array_search[$key]);
                }
            }*/



        if (in_array('titleabook', $filterselector)) {
          $titleModels[] = (new Book())->searchableAs();
        }
        if (in_array('titleinterstandard', $filterselector)) {
          $titleModels[] =  (new InterStandard())->searchableAs();
        }

        if (in_array('titlejuriprudence', $filterselector)) {
          $titleModels[] = (new Jurisprudence())->searchableAs();
        }
        if (in_array('titlelegalbody', $filterselector)) {
          $titleModels[] =  (new LegalBody())->searchableAs();
        }


        if (in_array('titlemagazine', $filterselector)) {
          $titleModels[] =  (new Magazine())->searchableAs();
        }
        if (in_array('titleregulation', $filterselector)) {
          $titleModels[] =   (new Regulation())->searchableAs();
        }
        if (in_array('titlereport', $filterselector)) {
          $titleModels[] =   (new Report())->searchableAs();
        }


        if (in_array('titleresearch', $filterselector)) {
          $titleModels[] =   (new Research())->searchableAs();
        }

        if (in_array('titleuniversitynewsletter', $filterselector)) {
          $titleModels[] =   (new UniversityNewsletter())->searchableAs();
        }

        if (in_array('titleinterconference', $filterselector)) {
          $titleModels[] =   (new AnnualInterConference())->searchableAs();
          $titleModels[] =   (new InterConference())->searchableAs();
          $titleModels[] =   (new InterConferenceRecommendation())->searchableAs();
        }

        if ($titleModels) {
          if ($array_search) {
            $array_search = array_intersect($array_search, $titleModels);
          } else {
            $array_search = $titleModels;
          }
        }
      }

      $textsearch .= ' AND (' . $request->input('titlesearch') . ' OR "' . $request->input('titlesearch') . '"^50)';
    }

    $textsearch = substr($textsearch, 5);
    if ($textsearch != '') {
      $textsearch = '' . $textsearch . ' AND is_valid:1 ';
      $results = MixedSearch::search(regenerateStemmerSearch($textsearch))->within(implode(',', $array_search))->paginate(25);
    }

    $languages = Language::all();
    $fields['title'] = $request->input('title');
    $fields['titlesearch'] = $request->input('titlesearch');
    $fields['sourcepublisher'] = $request->input('sourcepublisher');
    $fields['author'] = $request->input('author');
    $fields['second_title_one'] = $request->input('second_title_one');
    $fields['page_number'] = $request->input('page_number');

    return view('advancedsearch', [
      'results' => $results,
      'models' => $models,
      "modelsOptions" => $modelsOptions,
      'fields' => $fields,
      'filter1' => $request->input('filter1'),
      'selectsectorhidden' => $request->input('selectsectorhidden'),
      'languages' => $languages,
      'language' => $request->input('language'),
      'year' => $request->input('year'),
      'filter4' => $request->input('filter4'),
      'filter5' => $request->input('filter5'),
      'page_number' => is_numeric($request->input('page_number')), //;//($request->input('page_number')) ? '' :$request->input('page_number') ,
      'filterauthor' => $request->input('filterauthor'),
      'typeallrequest' => $request->input('typeallrequest'),
      'search' => ''

    ]);
  }

  public function advancedsearchNew(Request $request)
  {

    $textsearch = "";



    $modelsOptions = [
      "InterStandard"                   => __('adsearch_InterStandard'),
      "Jurisprudence"                   => __('adsearch_Jurisprudence'),
      "InterConferenceRecommendation"   => __('adsearch_InterConferenceRecommendation'),
      "LegalBody"                       => __('adsearch_LegalBody'),
      "AnnualInterConference"           => __('adsearch_AnnualInterConference'),
      "InterConference"                 => __('adsearch_InterConference'),
      "Book"                            => __('adsearch_Book'),
      "UniversityNewsletter"            => __('adsearch_UniversityNewsletter'),
      "Magazine"                        => __('adsearch_Magazine'),
      "Research"                        => __('adsearch_Research'),
      "Regulation"                      => __('adsearch_Regulation'),
      "Report"                          => __('adsearch_Report'),
    ];





    $models = Modelgroup::all();

    $results = [];
    $array_search = [
      (new AnnualInterConference())->searchableAs(),
      (new Book())->searchableAs(),
      (new InterConference())->searchableAs(),
      (new InterConferenceRecommendation())->searchableAs(),
      (new Magazine())->searchableAs(),
      (new Research())->searchableAs(),
      (new UniversityNewsletter())->searchableAs(),
      (new Regulation())->searchableAs(),
      (new Report())->searchableAs(),
      (new InterStandard())->searchableAs(),
      (new Jurisprudence())->searchableAs(),
      (new LegalBody())->searchableAs(),
    ];
    //filter models
    if ($request->input('filter1')) {
      $filter1 = explode(',', $request->input('filter1'));
      $filtermodels = '';
      foreach ($filter1 as $element) {
        if (in_array($element, ['AnnualInterConference', 'Book', 'InterConference', 'InterConferenceRecommendation', 'InterStandard', 'Jurisprudence', 'LegalBody', 'Magazine', 'Regulation', 'Report', 'Research', 'UniversityNewsletter'])) {
          $selctedsearchmodel = (new ('App\Models\\' . $element)())->searchableAs();
          if (in_array($selctedsearchmodel, $array_search, true)) {
            $element = 'App\Models\\' . $element;
            $filtermodels .= ',' . (new $element())->searchableAs();
          }
        } else redirect()->route('index.' . app()->getLocale());
      }
      if ($filtermodels != '') {
        $array_search = explode(',', $filtermodels);
        $array_search = array_filter($array_search);
      }
    }
    //end filter models



    //filter year
    $option_search_year = '';
    if ($request->input('year') && $request->input('year') != '') {

      //$filter2 = 'year1,year2,year3';
      $filter3 = explode(',', $request->input('year'));
      $filter3_content = '';
      foreach ($filter3 as $element) {

        $filter3_content .= 'OR year:' . $element . ' ';
      }
      $filter3_content = substr($filter3_content, 2);
      $filter3_content = substr($filter3_content, 1, -1);
      $option_search_year = ' AND (' . $filter3_content . ') ';
    }
    //end filter year

    $textsearch .= $option_search_year;
    if ($request->input('titlesearch') && $request->input('titlesearch') != "") {


      $title =  $this->replace_reserved($request->input('titlesearch'));
      $title = "" . $title . "";
      if ($request->input('selectsectorhidden') and $request->input('selectsectorhidden') != '') {
        if ($request->input('typeallrequest') == 'contains')
          $textsearch .= ' AND (title:' . $title . ')^70';
        if ($request->input('filter4') == 'exactvalue')
          $textsearch .= ' AND title:"' . $title . '"^70';
        if ($request->input('filter4') == 'anyword')
          $textsearch .= ' AND title:' . $title . '^70';
      } else {
        $textsearch .= ' AND (title:' . $title . ')';
      }
    }
    //dd($textsearch);
    if ($request->input('author') && $request->input('author') != "") {
      $author =  $this->replace_reserved($request->input('author'));
      $textsearch .= ' AND author:"' . $author . '"^40';
      /*
        if($request->input('filterauthor') and $request->input('filterauthor')!=''){
          if($request->input('filterauthor')=='contains')
          $textsearch .= ' AND +author:'.$author.'';
          if($request->input('filterauthor')=='exactvalue')
          $textsearch .= ' AND author:"'.$author.'"^40';
          if($request->input('filterauthor')=='anyword')
          $textsearch .= ' AND author:'.$author.'';
          }
          else
          {
            if($request->input('typeallrequest') && $request->input('typeallrequest')!='')
            {
              if($request->input('typeallrequest')=='contains')
              $textsearch .= ' AND +author:'.$author.'';
              if($request->input('typeallrequest')=='exactvalue')
              $textsearch .= ' AND author:"'.$author.'"^40';
              if($request->input('typeallrequest')=='anyword')
              $textsearch .= ' AND author:'.$author.'';
            }
            else
            $textsearch .= ' AND (author:'.$author.')';
          }*/
    }
    if ($request->input('second_title_one') && $request->input('second_title_one') != "") {
      $second_title_one =  $this->replace_reserved($request->input('second_title_one'));
      $textsearch .= ' AND (second_title_one:' . $second_title_one . ')';
      /*if($request->input('filter4') and $request->input('filter4')!=''){
          if($request->input('filter4')=='contains')
          $textsearch .= ' AND +second_title_one:'.$second_title_one.'';
          if($request->input('filter4')=='exactvalue')
          $textsearch .= ' AND second_title_one:"'.$second_title_one.'"^40';
          if($request->input('filter4')=='anyword')
          $textsearch .= ' AND second_title_one:'.$second_title_one.'';
          }
          else
          {
            if($request->input('typeallrequest') && $request->input('typeallrequest')!='')
            {
              if($request->input('typeallrequest')=='contains')
              $textsearch .= ' AND +second_title_one:'.$second_title_one.'';
              if($request->input('typeallrequest')=='exactvalue')
              $textsearch .= ' AND second_title_one:"'.$second_title_one.'"^40';
              if($request->input('typeallrequest')=='anyword')
              $textsearch .= ' AND second_title_one:'.$second_title_one.'';
            }
            else
            $textsearch .= ' AND (second_title_one:'.$second_title_one.')';

          }*/
    }
    if ($request->input('filter5') && $request->input('filter5') != "" && $request->input('page_number') && $request->input('page_number') != "") {
      $page_number =  $this->replace_reserved($request->input('page_number'));
      $page_number = is_numeric($page_number) ? $page_number : false;
      $textsearch .= ' AND (page_number:=' . $page_number . ')';
      /* if($page_number)
        if($request->input('filter5') && $request->input('filter5')!=''){

         if($request->input('filter5')=='less')
          $textsearch .= ' AND page_number:<'.(int)$page_number.'';
          if($request->input('filter5')=='equal')
          $textsearch .= ' AND page_number:='.(int)$page_number.'^40';
          if($request->input('filter5')=='more')
          $textsearch .= ' AND page_number:>'.(int)$page_number.'';
          }
          else
          {
            $textsearch .= ' AND (page_number:='.$page_number.')';
          }*/
    }
    if ($request->input('sourcepublisher') && $request->input('sourcepublisher') != "") {
      $sourcepublisher =  $this->replace_reserved($request->input('sourcepublisher'));


      if ($sourcepublisher)
        $textsearch .= ' AND (publisher:"' . $sourcepublisher . '"^40 OR author:"' . $sourcepublisher . '"^40  OR university:"' . $sourcepublisher . '"^40  OR country:"' . $sourcepublisher . '"^40 )';
    }
    //dd($request->input('selectsectorhidden'));
    if ($request->input('titlesearch') && $request->input('titlesearch') != "") {
      if ($request->input('selectsectorhidden') && $request->input('selectsectorhidden') != "") {
        $filterselector = explode(',', $request->input('selectsectorhidden'));
        $filter1 = explode(',', $request->input('filter1'));
        $titleModels = [];
        $textsearchselector = '';
        /*if(!in_array('titleannualinterconference',$filterselector)){
                if (($key = array_search('annual_inter_conferences', $array_search)) !== false) {
                    unset($array_search[$key]);
                }
            }*/
        /*if(!in_array('titleinterconferencerecommendation',$filterselector)){
                if (($key = array_search('inter_conference_recommendations', $array_search)) !== false) {
                    unset($array_search[$key]);
                }
            }*/

        //  dd( array_key_exists('books',$filter1));

        //
        //
        //
        //,
        //
        //
        //Book
        //
        //
        //
        //
        //
        if (in_array('titlebook', $filterselector)) {
          $group = $this->checkgroupexists('books', $array_search);
          if ($group && in_array('Book', $filter1))
            $titleModels[] = (new Book())->searchableAs();
        }
        if (in_array('titleinterstandard', $filterselector)) {
          $group = $this->checkgroupexists('inter_standards', $array_search);
          if ($group && in_array('InterStandard', $filter1))
            $titleModels[] =  (new InterStandard())->searchableAs();
        }

        if (in_array('titlejuriprudence', $filterselector)) {
          $group = $this->checkgroupexists('inter_conference_recommendations', $array_search);
          if ($group  && in_array('InterConferenceRecommendation', $filter1))
            $titleModels[] =   (new InterConferenceRecommendation())->searchableAs();
          $group = $this->checkgroupexists('annual_inter_conferences', $array_search);
        }
        if (in_array('titlelegalbody', $filterselector)) {
          $group = $this->checkgroupexists('legal_bodies', $array_search);
          if ($group && in_array('LegalBody', $filter1))
            $titleModels[] =  (new LegalBody())->searchableAs();
        }


        if (in_array('titlemagazine', $filterselector)) {
          $group = $this->checkgroupexists('magazines', $array_search);
          if ($group && in_array('Magazine', $filter1))
            $titleModels[] =  (new Magazine())->searchableAs();
        }
        if (in_array('titleregulation', $filterselector)) {
          $group = $this->checkgroupexists('regulations', $array_search);
          if ($group  && in_array('Regulation', $filter1))
            $titleModels[] =   (new Regulation())->searchableAs();
        }
        if (in_array('titlereport', $filterselector)) {
          $group = $this->checkgroupexists('reports', $array_search);
          if ($group  && in_array('Report', $filter1))
            $titleModels[] =   (new Report())->searchableAs();
        }


        if (in_array('titleresearch', $filterselector)) {
          $group = $this->checkgroupexists('researches', $array_search);
          if ($group  && in_array('Research', $filter1))
            array_push($titleModels, (new Research())->searchableAs());
        }

        if (in_array('titleuniversitynewsletter', $filterselector)) {
          $group = $this->checkgroupexists('university_newsletters', $array_search);
          if ($group  && in_array('UniversityNewsletter', $filter1))
            $titleModels[] =   (new UniversityNewsletter())->searchableAs();
        }

        if (in_array('titleinterconference', $filterselector)) {
          $group = $this->checkgroupexists('inter_conference_recommendations', $array_search);
          if ($group  && in_array('InterConferenceRecommendation', $filter1))
            $titleModels[] =   (new InterConferenceRecommendation())->searchableAs();
          $group = $this->checkgroupexists('annual_inter_conferences', $array_search);
          if ($group && in_array('AnnualInterConference', $filter1))
            $titleModels[] =   (new AnnualInterConference())->searchableAs();
          $group = $this->checkgroupexists('inter_conferences', $array_search);

          // dd($filter1,in_array('InterConference',$filter1));
          if ($group && in_array('InterConference', $filter1))
            $titleModels[] =   (new InterConference())->searchableAs();
        }


        if ($titleModels) {
          if ($array_search) {
            $array_search = array_intersect($array_search, $titleModels);
            //$array_search = array_intersect($array_search, $filter1);

          } else {
            $array_search = $titleModels;
          }
        }
        $newelementtosearch = [];
        if ($filterselector) {
          foreach ($array_search as $elementmodelsearch) {
            if (in_array($elementmodelsearch, $titleModels)) {
              $newelementtosearch[] = $elementmodelsearch;
            }
          }
          $array_search = $newelementtosearch;
        }
      }
      if ($request->input('titlesearch') && $request->input('titlesearch') != "") {
        $titlesearch = $request->input('titlesearch');
        function containsArabicLetters($words, $arabicLetters)
        {
          $wordsUnicode = preg_replace('//u', '', $words);
          $arabicLettersUnicode = array_map(function ($letter) {
            return mb_convert_encoding($letter, 'UTF-8', 'UTF-8');
          }, $arabicLetters);

          foreach ($arabicLettersUnicode as $letter) {
            if (mb_strpos($wordsUnicode, $letter) !== false) {
              return true;
            }
          }

          return false;
        }
        if ($titlesearch) {
          $words = explode(" ", $titlesearch);

          $arabicLetters = [
            'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
            'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
          ];
          $modifiedWords = array_map(function ($word) use ($arabicLetters) {
            if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetters($word, $arabicLetters)) {
              $word = "ال" . $word;
            }
            return $word;
          }, $words);
          if (count($words) == 1) {
            $titlesearch = implode(" ", $modifiedWords);
          }
          $searchWord = $this->replace_reserved($request->input('search'));
          if ($searchWord == "على") {
            $titlesearch = "result_should_be_null";
          }
        }
        $searchlike =  "*" . $titlesearch . "*";
        $searchlike = preg_replace('/\b\w{1,2}\b\s*/u', '', $titlesearch);


        $score = ' ( (' . $titlesearch . ') OR

                    ("' . $searchlike . '")^100 OR

                    title:("' . $titlesearch . '")^150 OR
                    author:("' . $titlesearch . '")^140 OR
                    main_title:("' . $titlesearch . '")^100 OR
                    secondary_title_one:("' . $titlesearch . '")^100 OR
                    secondary_title_two:("' . $titlesearch . '")^100 OR

                    organiser:("' . $titlesearch . '")^70 OR
                    conference_title:("' . $titlesearch . '")^70 OR
                    session_title:("' . $titlesearch . '")^70 OR
                    chapter_title:("' . $titlesearch . '")^70 OR
                    search_title:("' . $titlesearch . '")^70 OR
                    request_title:("' . $titlesearch . '")^70 OR
                    publisher:("' . $titlesearch . '")^70 OR
                    act_title:("' . $titlesearch . '")^70 OR
                    request_title:("' . $titlesearch . '")^70 OR
                    publisher:("' . $titlesearch . '")^70

                    )';
      }

      //dd($filter1,$filterselector,$titleModels,$array_search);
      $textsearch .= ' AND (' . $request->input('titlesearch') . ' OR "' . $request->input('titlesearch') . '"^50)';
    }
    $textsearch = substr($textsearch, 5);
    //dd($textsearch);
    //dd($array_search);
    if ($textsearch != '') {
      $textsearch = '' . $textsearch . ' AND is_valid:1 ';


      $queryString = $this->generateQuerySearch(
        [
          'search'          => $this->replace_reserved($request->input('titlesearch')),
          'sourcepublisher' => $request->input('sourcepublisher'),
          'titleModel'      => $request->input('selectsectorhidden'),
          'author'          => $request->input('author'),
          'year'            => $request->input('year'),
          'typeRequest'     => $request->input('typeallrequest'),
          'language'        => $request->input('language'),
          'pagesOperator'   => $request->input('filter5'),
          'page_number'     => $request->input('page_number'),
        ],
        $array_search
      );
      if (str_contains(substr($queryString, 0, 5), 'AND')) {

        $textsearch = substr($queryString, 5);
      } else {

        $textsearch = $queryString;
      }

      if ($array_search)
        $results = MixedSearch::search(regenerateStemmerSearch($textsearch))->within(implode(',', $array_search))->paginate(10000);
    }
    function clean($word)
    {
      $cleaned = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $word));
      return $cleaned;
    }
    if ($results) {
      $perPage = 300;
      $total = $results->total();
      $currentPage = $results->currentPage();
      //working on items
      $items = $results->items();
      $matchingItems = [];
      $containingItems = [];
      $nonMatchingItems = [];
      $searchWord = $request->input('titlesearch');
      if ($searchWord) {
        $words = explode(" ", $searchWord);

        $arabicLetters = [
          'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
          'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
        ];
        $modifiedWords = array_map(function ($word) use ($arabicLetters) {
          if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetters($word, $arabicLetters)) {
            $word = "ال" . $word;
          }
          return $word;
        }, $words);
        if (count($words) == 1) {
          $searchWord = implode(" ", $modifiedWords);
        }
        $wordSearch = $this->replace_reserved($request->input('search'));
        if ($wordSearch == "على") {
          $searchWord = "result_should_be_null";
        }
      }
      $searchType = $request->input('typeallrequest');
      $cleanedInput = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا',  str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $searchWord));
      if ($searchType === 'exactvalue') {
        foreach ($items as $result) {
          $title = $result->getTitle();
          $author = $result["author"];
          $publisher = $result["publisher"];
          $university = $result["university"];
          $country = $result["country"];
          $secondaryTitleOne = $result["secondary_title_one"];

          $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));
          if ($cleanedTitle == $cleanedInput || $secondaryTitleOne == $searchWord || $author == $searchWord || $publisher == $searchWord) {
            if (isset($sourcepublisher)) {
              if ($sourcepublisher == $author || $sourcepublisher == $publisher || $sourcepublisher == $university || $sourcepublisher == $country) {
                $matchingItems[] = $result;
              }
            } else {
              $matchingItems[] = $result;
            }
          }
        }
      } elseif ($searchType === 'contains') {
        foreach ($items as $result) {
          $title = $result->getTitle();
          $author = $result["author"];
          $publisher = $result["publisher"];
          $university = $result["university"];
          $country = $result["country"];
          $secondaryTitleOne = $result["secondary_title_one"];

          $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));
          if ($cleanedTitle == $cleanedInput || $secondaryTitleOne == $searchWord || $author == $searchWord || $publisher == $searchWord) {
            if (isset($sourcepublisher)) {
              if ($sourcepublisher == $author || $sourcepublisher == $publisher || $sourcepublisher == $university || $sourcepublisher == $country) {
                $matchingItems[] = $result;
              }
            } else {
              $matchingItems[] = $result;
            }
          } elseif (str_contains($cleanedTitle, $cleanedInput) || str_contains($secondaryTitleOne, $searchWord) || str_contains($author, $searchWord) || str_contains($publisher, $searchWord)) {
            if (isset($sourcepublisher)) {
              if (str_contains($author, $sourcepublisher) || str_contains($publisher, $sourcepublisher) || str_contains($university, $sourcepublisher) || str_contains($country, $sourcepublisher)) {
                $containingItems[] = $result;
              }
            } else {
              $containingItems[] = $result;
            }
          }
        }
      } else {
        foreach ($items as $result) {
          $title = $result->getTitle();
          $author = $result["author"];
          $publisher = $result["publisher"];
          $university = $result["university"];
          $country = $result["country"];
          $secondaryTitleOne = $result["secondary_title_one"];

          $cleanedTitle = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', str_replace(['ٌ', 'ً', 'َ', 'ُ', 'ِ', 'ّ'], '', $title));
          if ($cleanedTitle == $cleanedInput || $secondaryTitleOne == $searchWord || $author == $searchWord || $publisher == $searchWord) {
            if (isset($sourcepublisher)) {
              if ($sourcepublisher == $author || $sourcepublisher == $publisher || $sourcepublisher == $university || $sourcepublisher == $country) {
                $matchingItems[] = $result;
              }
            } else {
              $matchingItems[] = $result;
            }
          } elseif (str_contains($cleanedTitle, $cleanedInput) || str_contains($secondaryTitleOne, $searchWord) || str_contains($author, $searchWord) || str_contains($publisher, $searchWord)) {
            if (isset($sourcepublisher)) {
              if (str_contains($author, $sourcepublisher) || str_contains($publisher, $sourcepublisher) || str_contains($university, $sourcepublisher) || str_contains($country, $sourcepublisher)) {
                $containingItems[] = $result;
              }
            } else {
              $containingItems[] = $result;
            }
          } else {
            $nonMatchingItems[] = $result;
          }
        }
      }
      $items = array_merge($matchingItems, $containingItems, $nonMatchingItems);
      $total = count($items);
      $items = array_slice($items, ($currentPage - 1) * $perPage, $currentPage * $perPage);

      $paginatedResults = new \Illuminate\Pagination\LengthAwarePaginator(
        $items,
        $total,
        $perPage,
        $currentPage,
        ['path' => request()->url(), 'query' => request()->query()]
      );
      $results = $paginatedResults;
    }

    $languages = Language::all();
    $fields['title'] = $request->input('title');
    $fields['titlesearch'] = $request->input('titlesearch');
    $fields['sourcepublisher'] = $request->input('sourcepublisher');
    $fields['author'] = $request->input('author');
    $fields['second_title_one'] = $request->input('second_title_one');
    $fields['page_number'] = $request->input('page_number');

    return view('advancedsearch', [
      'results' => $results,
      'models' => $models,
      "modelsOptions" => $modelsOptions,
      'fields' => $fields,
      'filter1' => $request->input('filter1'),
      'selectsectorhidden' => $request->input('selectsectorhidden'),
      'languages' => $languages,
      'language' => $request->input('language'),
      'year' => $request->input('year'),
      'filter4' => $request->input('filter4'),
      'filter5' => $request->input('filter5'),
      'page_number' => is_numeric($request->input('page_number')), //;//($request->input('page_number')) ? '' :$request->input('page_number') ,
      'filterauthor' => $request->input('filterauthor'),
      'typeallrequest' => $request->input('typeallrequest'),
      'search' => ''

    ]);
  }

  private function checkgroupexists($namegroup = '', $array_search)
  {
    if ($namegroup == '')
      return false;

    $valueexists = 0;
    foreach ($array_search as $keyelement => $elementsearch) {
      if ($elementsearch == $namegroup)
        $valueexists = 1;
    }
    if ($valueexists == 1)
      return true;

    return false;
  }
  private function replace_reserved(String $element = null)
  {
    if ($element != null) {
      $search  = array(':', '+', '/', '\\', '?', '<', '>', '"', '\'', '(', ')', '^', '~', '{', '}', '[', '-', ']');
      $element = str_replace($search, '', $element);
      return $element;
    }
    return '';
  }

  public function generateQuerySearch($entries, $array_search)
  {
    $searchKeys       = $entries['search'] ?? "";
    $sourcepublisher  = $entries['sourcepublisher'] ?? "";
    $titleModel       = $entries['titleModel'] ?? "";
    $year             = $entries['year'] ?? "";
    $author           = $entries['author'] ?? "";
    $typeRequest      = $entries['typeRequest'] ?? "";
    $language         = $entries['language'] ?? "";
    $pagesOperator    = $entries['pagesOperator'] ?? "";
    $page_number      = $entries['page_number'] ?? "";


    if ($searchKeys) {
      $wordSearch = $searchKeys;
      $words = explode(" ", $searchKeys);

      $arabicLetters = [
        'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص',
        'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'
      ];
      $modifiedWords = array_map(function ($word) use ($arabicLetters) {
        if (mb_substr($word, 0, 2) !== "ال" && containsArabicLetters($word, $arabicLetters)) {
          $word = "ال" . $word;
        }
        return $word;
      }, $words);
      if (count($words) == 1) {
        $searchKeys = implode(" ", $modifiedWords);
      }
      if ($wordSearch == "على") {
        $searchKeys = "result_should_be_null";
      }
    }
    if ($searchKeys) {
      $query = '(' . $searchKeys . ' OR "' . $searchKeys . '"^40 OR ( title:(' . $searchKeys . ')^70 OR author:(' . $searchKeys . ')^7 OR secondary_title_one:(' . $searchKeys . ')^5)) AND is_valid:1';
      if ($typeRequest == 'contains')
        $query = '(' . $searchKeys . ' OR "' . $searchKeys . '"^40 OR ( title:(' . $searchKeys . ')^70 OR author:(' . $searchKeys . ')^7 OR secondary_title_one:(' . $searchKeys . ')^5)) AND is_valid:1';

      if ($typeRequest == 'exactvalue')
        $query = '(' . $searchKeys . ' OR "' . $searchKeys . '"^40 OR ( title:(' . $searchKeys . ')^70 OR author:(' . $searchKeys . ')^7 OR secondary_title_one:(' . $searchKeys . ')^5)) AND is_valid:1';

      if ($typeRequest == 'anyword')
        $query = '(' . $searchKeys . ' OR "' . $searchKeys . '"^40 OR ( title:(' . $searchKeys . ')^70 OR author:(' . $searchKeys . ')^7 OR secondary_title_one:(' . $searchKeys . ')^5)) AND is_valid:1';
    } else {
      $query = '';
    }



    if ($titleModel && $searchKeys) {

      $query = '(title:(*' . $searchKeys . '*)^70 ) AND is_valid:1';

      if ($typeRequest == 'contains')
        $query = '(title:' . $searchKeys . ')^70  AND is_valid:1';

      if ($typeRequest == 'exactvalue')
        $query = '(title:("' . $searchKeys . '")^70 ) AND is_valid:1';

      if ($typeRequest == 'anyword')
        $query = '(title:(' . $searchKeys . ')^70 ) AND is_valid:1';
    }


    // if($sourcepublisher){
    //     $query .= ' AND (publisher:(*'.$sourcepublisher.'*)^40 OR author:(*'.$sourcepublisher.'*)^40  OR university:(*'.$sourcepublisher.'*)^40  OR country:(*'.$sourcepublisher.'*)^40 )';

    //     if($typeRequest=='contains')
    //       $query .= ' AND (publisher:(*'.$sourcepublisher.'*)^40 OR author:(*'.$sourcepublisher.'*)^40  OR university:(*'.$sourcepublisher.'*)^40  OR country:(*'.$sourcepublisher.'*)^40 )';

    //     if($typeRequest=='exactvalue')
    //       $query .= ' AND (publisher:"'.$sourcepublisher.'"^40 OR author:"'.$sourcepublisher.'"^40  OR university:"'.$sourcepublisher.'"^40  OR country:"'.$sourcepublisher.'"^40 )';

    //     if($typeRequest=='anyword')
    //       $query .= ' AND (publisher:'.$sourcepublisher.'^40 OR author:'.$sourcepublisher.'^40  OR university:'.$sourcepublisher.'^40  OR country:'.$sourcepublisher.'^40 )';

    // }


    if ($year) {

      $query .= ' AND year:' . $year;

      if ($typeRequest == 'contains')
        $query .= ' AND year:(*' . $year . '*)';
      if ($typeRequest == 'exactvalue')
        $query .= ' AND year:"' . $year . '"';
      if ($typeRequest == 'anyword')
        $query .= ' AND year:' . $year;
    }


    if ($author) {
      $query .= ' AND author:' . $author;
      if ($typeRequest == 'contains')
        $query .= ' AND author:(*' . $author . '*)';
      if ($typeRequest == 'exactvalue')
        $query .= ' AND author:"' . $author . '"';
      if ($typeRequest == 'anyword')
        $query .= ' AND author:' . $author;
    }



    if ($page_number) {
      if ($pagesOperator) {
        if ($pagesOperator == 'less') {
          $query .= ' AND page_number:<' . (int)$page_number . '';
        } elseif ($pagesOperator == 'more') {
          $query .= ' AND page_number:>' . (int)$page_number . '';
        } else {
          $query .= ' AND page_number:' . $page_number . '';
        }
      } else {
        $query .= ' AND page_number:' . $page_number . '';
      }
    }


    if ($language) {
      $langs = explode(',', $language);
      $filter_language = '';
      if ($langs && sizeof($langs) < 3) {
        foreach ($langs as $lang) {

          $filter_language .= 'OR language_id:' . (int)$lang . ' ';
        }
        $filter_language = substr($filter_language, 2);
        $filter_language = substr($filter_language, 1, -1);
        $query .= ' AND (' . $filter_language . ') ';
      }
    }
    return $query;
  }
  public function details($model, $id)
  {
    $result = [];
    $models = Modelgroup::all();
    $model = Modelgroup::where('slug', $model)->first();
    if ($model) {
      $item = "App\Models\\" . $model->name;
      $result = $item::where('slug', $id)->where('is_valid', 1)->first();
    }
    $sourcegroup = Sourcegroup::find($result->sourcegroup_id);
    $group = Modelgroup::where('class', $sourcegroup->groupnumber)->first();
    //dd($sourcegroup);
    seo()->addMany([


      Meta\OpenGraph::make()
        ->property('title')
        ->content(__($group?->title)),

      Meta\Twitter::make()
        ->name('title')
        ->content(__($result->FIELDS['title'][app()->getLocale()] . ' : ' . $group?->title)),

    ]);

    $ograph = '';
    switch ($result->getClassname()) {

      case 'AnnualInterConference':
        $ograph = $result->FIELDS['organiser'][app()->getLocale()] . ' : ' . $result->getorganiser();
        break;
      case 'Book':
      case 'InterConference':
      case 'InterConferenceRecommendation':
      case 'InterStandard':
      case 'Jurisprudence':
      case 'LegalBody':
      case 'Magazine':
      case 'Regulation':
      case 'Report':
      case 'UniversityNewsletter':
        $ograph =  $result->FIELDS['author'][app()->getLocale()] . ' : ' . $result->author;
        break;
      case 'Research':
        $ograph =  $result->FIELDS['publisher'][app()->getLocale()] . ' : ' . $result->publisher;
        break;
    }

    if (array_key_exists('organiser', $result->FIELDS)) {
      $secondtitle = $result->getorganiser();
    } elseif (array_key_exists('author', $result->FIELDS)) {
      $secondtitle = $result->getauthor();
    }
    seo()->addMany([

      Meta\Description::make()
        ->name('description')
        ->content($result->FIELDS['title'][app()->getLocale()] . ' : ' . $result?->getTitle() . ' | ' . $ograph),

      Meta\OpenGraph::make()
        ->property('description')
        ->content($result->FIELDS['title'][app()->getLocale()] . ' : ' . $result?->getTitle() . ' | ' . $ograph),

      Meta\Twitter::make()
        ->name('description')
        ->content($result->FIELDS['title'][app()->getLocale()] . ' : ' . $result?->getTitle() . ' | ' . $ograph),

    ]);
    seo()->addMany([
      Meta::make()
        ->name('image')
        ->content('/storage/logoog.png'),

      Meta\OpenGraph::make()
        ->property('image')
        ->content('/storage/logoog.png'),

      Meta\Twitter::make()
        ->name('image')
        ->content('/storage/logoog.png'),

    ]);
    return view('ordreddetails', ['result' => $result, 'models' => $models]);
  }





  public function getproprieties($results)
  {
    $exporters = [];
    $years_release = [];
    $authors = [];

    foreach ($results as $model_result) {
      if (!in_array($model_result->getexporter(), $exporters, true)) {
        if ($model_result->getexporter() != '')
          array_push($exporters, $model_result->getexporter());
      }

      if (!in_array($model_result->year, $years_release, true)) {
        if ($model_result->year != '')
          array_push($years_release, $model_result->year);
      }

      if (!in_array($model_result->author, $authors, true)) {
        if ($model_result->author != '')
          array_push($authors, $model_result->author);
      }
    }

    $proprities['exporters'] = $exporters;
    $proprities['years_release'] = $years_release;
    $proprities['authors'] = $authors;
    return $proprities;
  }
  public function contact(Request $request)
  {

    return view('contact');
  }
  public function aboutus(Request $request)
  {
    if (app()->getLocale() == 'ar') {
      return view('aboutus');
    }
    return view('aboutus_en');
  }
  public function aboutdatabase(Request $request)
  {
    if (app()->getLocale() == 'ar') {
      return view('aboutdatabase');
    }
    return view('aboutdatabase_en');
  }


  public function userguide(Request $request)
  {
    if (app()->getLocale() == 'ar') {
      return view('userguide');
    }
    return view('userguide_en');
  }


  public function storecontact(Request $request)
  {
    // Log the request for debugging
    \Log::info('Contact form submission attempt', [
      'ip' => $request->ip(),
      'user_agent' => $request->userAgent(),
      'has_data' => !empty($request->all()),
      'data_keys' => array_keys($request->all())
    ]);

    try {
      // Early validation to prevent empty submissions
      if (empty($request->all()) || count(array_filter($request->only(['name', 'email', 'pays', 'institution', 'phone', 'sujet', 'object']))) === 0) {
        \Log::warning('Empty contact form submission blocked', [
          'ip' => $request->ip(),
          'user_agent' => $request->userAgent()
        ]);

        return redirect()->back()
          ->withInput()
          ->with('error', __('validation.contact_form_empty'));
      }

      $validatedData = $request->validate([
        'name' => ['required', 'string', 'max:255', 'regex:/^[\p{L}\s-]+$/u'],
        'email' => 'required|email|max:255',
        'pays' => 'required|string|max:100',
        'institution' => 'required|string|max:255',
        'phone' => ['required', 'string', 'max:20', 'regex:/^[\+]?[0-9\s\-\(\)\.]+$/'],
        'sujet' => 'required|string|max:255',
        'object' => 'required|string|max:1000',
      ], [
        'name.required' => __('validation.required', ['attribute' => __('validation.attributes.name')]),
        'name.regex' => __('validation.name_regex'),
        'email.required' => __('validation.required', ['attribute' => __('validation.attributes.email')]),
        'email.email' => __('validation.email', ['attribute' => __('validation.attributes.email')]),
        'pays.required' => __('validation.required', ['attribute' => __('validation.attributes.pays')]),
        'institution.required' => __('validation.required', ['attribute' => __('validation.attributes.institution')]),
        'phone.required' => __('validation.required', ['attribute' => __('validation.attributes.phone')]),
        'phone.regex' => __('validation.phone_regex'),
        'sujet.required' => __('validation.required', ['attribute' => __('validation.attributes.sujet')]),
        'object.required' => __('validation.required', ['attribute' => __('validation.attributes.object')]),
        'sujet.max' => __('validation.max', ['attribute' => __('validation.attributes.sujet'), 'max' => 255]),
        'object.max' => __('validation.max', ['attribute' => __('validation.attributes.object'), 'max' => 1000]),
      ]);

      \Log::info('Contact form validation passed', [
        'email' => $validatedData['email'],
        'name' => $validatedData['name']
      ]);

    } catch (\Illuminate\Validation\ValidationException $e) {
      \Log::warning('Contact form validation failed', [
        'ip' => $request->ip(),
        'errors' => $e->validator->errors()->toArray(),
        'input' => $request->except(['_token'])
      ]);

      return redirect()->back()
        ->withErrors($e->validator)
        ->withInput()
        ->with('error', __('validation.contact_form_error'));
    } catch (\Exception $e) {
      \Log::error('Contact form submission error: ' . $e->getMessage(), [
        'ip' => $request->ip(),
        'exception' => $e->getTraceAsString(),
        'input' => $request->except(['_token'])
      ]);

      return redirect()->back()
        ->withInput()
        ->with('error', __('validation.contact_form_error'));
    }

    try {
      // Create contact record with transaction
      \DB::beginTransaction();

      $contact = Contact::create($validatedData);

      if (!$contact) {
        throw new \Exception('Failed to create contact record');
      }

      \DB::commit();

      \Log::info('Contact record created successfully', [
        'contact_id' => $contact->id,
        'email' => $contact->email
      ]);

      $message = "message_sent";

    } catch (\Exception $e) {
      \DB::rollback();

      \Log::error('Failed to create contact record: ' . $e->getMessage(), [
        'ip' => $request->ip(),
        'exception' => $e->getTraceAsString(),
        'validated_data' => $validatedData
      ]);

      return redirect()->back()
        ->withInput()
        ->with('error', __('validation.contact_form_error'));
    }

    // Handle webhook with timeout and error handling
    try {
      $webhookUrl = rtrim(env("APP_URL"), '/') . '/webhook/57ff8fd2-658e-4fb1-aa22-a7bc3dd6d77b';

      $webhookResponse = Http::timeout(5) // 5 second timeout
        ->retry(2, 1000) // Retry twice with 1 second delay
        ->post($webhookUrl, ['email' => $validatedData['email']]);

      \Log::info('Webhook called successfully', [
        'contact_id' => $contact->id,
        'webhook_status' => $webhookResponse->status(),
        'webhook_response' => $webhookResponse->body()
      ]);

    } catch (\Exception $e) {
      // Don't fail the entire request if webhook fails
      \Log::warning('Webhook call failed but contact was saved', [
        'contact_id' => $contact->id,
        'webhook_error' => $e->getMessage(),
        'email' => $validatedData['email']
      ]);
    }

    return redirect()->back()->with('message', $message);
  }

  public function trucatefinancedata()
  {
    Artisan::call("financedata:truncate");
  }
  public function validateall()
  {
    Artisan::call("validateall:all");
  }
  public function unvalidateall()
  {
    Artisan::call("unvalidateall:all");
  }


  public function countrecordmodel($id)
  {
    $recordedmodel = Modelgroup::where('class', $id)->first();
    $recordedmodel =  '\App\Models\\' . $recordedmodel->name;

    $records =   $recordedmodel::count();
    return $records;
  }
  public function countrecordmodelexcel($id)
  {
    $nbitemexcel = 0;
    $modelid = Modelgroup::where('class', $id)->first();
    $data = Filesource::where('modelgroups_id', $modelid->id)->get();
    foreach ($data as $item) {
      $nbitemexcel += $item->exceldata;
    }
    $recordedmodel = Modelgroup::where('class', $id)->first();
    $recorded =  '\App\Models\\' . $recordedmodel->name;

    $records =   $recorded::count();

    $arrayrecords = [
      'classname' => $recordedmodel->name,
      'nbitemexcel' => $nbitemexcel,
      'records' => $records
    ];
    return $arrayrecords;
    //return  Book::count();
  }
}
