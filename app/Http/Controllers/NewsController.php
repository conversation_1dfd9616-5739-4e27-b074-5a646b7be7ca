<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{

    public function index()
    {
        $news = News::where('status', '=', 1)
        ->orderBy('created_at', 'desc') // Order by created_at in descending order
        ->paginate(25);
    
    return view('news', compact('news'));
    }

    public function show( $news){


        $news = News::where('slug','=',$news)->first();
        return view('sections.detailnewsbox',['articlenews'=>$news]);
    }

}
