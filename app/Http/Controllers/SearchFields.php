<?php

namespace App\Http\Controllers;

class SearchFields
{
     const FIELDS = [
        'TITLE' => ["exactscore" => 300, "containsscore" => 150],
        'AUTHOR' => ["exactscore" => 280, "containsscore" => 140],
        'MAIN_TITLE' => ["exactscore" => 200, "containsscore" => 100],
        'SECONDARY_TITLE_ONE' => ["exactscore" => 200, "containsscore" => 100],
        'SECONDARY_TITLE_TWO' => ["exactscore" => 200, "containsscore" => 100],
        'ORGANISER' => ["exactscore" => 140, "containsscore" => 70],
        'CONFERENCE_TITLE' => ["exactscore" => 140, "containsscore" => 70],
        'SESSION_TITLE' => ["exactscore" => 140, "containsscore" => 70],
        'CHAPTER_TITLE' => ["exactscore" => 140, "containsscore" => 70],
        'SEARCH_TITLE' => ["exactscore" => 140, "containsscore" => 70],
        'REQUEST_TITLE' => ["exactscore" => 140, "containsscore" => 70],
        'PUBLISHER' => ["exactscore" => 140, "containsscore" => 70],
        'ACT_TITLE' => ["exactscore" => 140, "containsscore" => 70],
     ];
}
