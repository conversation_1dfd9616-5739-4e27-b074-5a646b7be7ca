<?php

namespace App\Http\Controllers;

use App\Models\Indicedetail;
use Illuminate\Http\Request;

class IndicedetailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Indicedetail  $indicedetail
     * @return \Illuminate\Http\Response
     */
    public function show(Indicedetail $indicedetail)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Indicedetail  $indicedetail
     * @return \Illuminate\Http\Response
     */
    public function edit(Indicedetail $indicedetail)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Indicedetail  $indicedetail
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Indicedetail $indicedetail)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Indicedetail  $indicedetail
     * @return \Illuminate\Http\Response
     */
    public function destroy(Indicedetail $indicedetail)
    {
        //
    }
}
