<?php

namespace App\Http\Controllers;

use App\Mail\ExampleMicrosoftGraphMail;
use App\Services\MicrosoftGraphMailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class MicrosoftGraphMailController extends Controller
{
    protected $microsoftGraphService;

    public function __construct(MicrosoftGraphMailService $microsoftGraphService)
    {
        $this->microsoftGraphService = $microsoftGraphService;
    }

    /**
     * Test Microsoft Graph connection
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testConnection()
    {
        try {
            $isConnected = $this->microsoftGraphService->testConnection();
            
            return response()->json([
                'success' => $isConnected,
                'message' => $isConnected 
                    ? 'Microsoft Graph connection successful' 
                    : 'Microsoft Graph connection failed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test email using Microsoft Graph
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTestEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'to' => 'required|email',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create a simple user object for the email
            $user = (object) [
                'name' => 'Test User',
                'email' => $request->to
            ];

            // Send email using the mailable class
            Mail::mailer('microsoft-graph')
                ->to($request->to)
                ->send(new ExampleMicrosoftGraphMail($user, $request->message));

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully via Microsoft Graph'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send email using direct service method
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendDirectEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'to' => 'required|email',
            'subject' => 'required|string|max:255',
            'body' => 'required|string',
            'body_type' => 'sometimes|in:HTML,Text'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $emailData = [
                'to' => [['email' => $request->to]],
                'subject' => $request->subject,
                'body' => $request->body,
                'body_type' => $request->body_type ?? 'HTML',
                'from' => config('mail.from.address')
            ];

            $success = $this->microsoftGraphService->sendEmail($emailData);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email sent successfully via Microsoft Graph'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send email'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send email: ' . $e->getMessage()
            ], 500);
        }
    }
}
