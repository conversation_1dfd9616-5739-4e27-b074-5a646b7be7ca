<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Scoutimportjob;
use App\Models\Filesource;
use App\Models\Modelgroup;
use Artisan;
use Illuminate\Http\Request;

class FilesourceController extends Controller
{
    public function updatestatus(Request $request)
    {

        $file = explode('/',$request->input('filename'));
        $exceldata = $request->input('exceldata');
        $filename = end($file);
        $filesource = Filesource::where('filename',$filename)->first();

        if($filesource){
            $filesource->is_updated = true;
            $filesource->is_error = false;
            $filesource->exceldata = $exceldata;

            $filesource->save();
            Scoutimportjob::dispatch();
            return response(201);
        }

        return response(403);
    }
    public function updatecomment(Request $request)
    {

        $file = explode('/',$request->input('filename'));
        $exceldata = $request->input('exceldata');
        $filename = end($file);

        $modelgroup = Modelgroup::where('slug','=',$request->input('modelgroup_slug'))->first();
        $filesource = Filesource::where('modelgroups_id',$modelgroup->id)->latest()->first();

        if($filesource){
           // $filesource->comment = __($request->input('comment'));
           // $filesource->exceldata = $exceldata;
           $filesource->comment = __('Check the file format');
           $filesource->is_updated = true;
           $filesource->is_error = true;
           
            $filesource->save();
            Scoutimportjob::dispatch();
            return response(201);
        }

        return response(403);
    }

}
