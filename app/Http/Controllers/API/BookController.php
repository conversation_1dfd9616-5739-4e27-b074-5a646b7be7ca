<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\BookResource;
use App\Jobs\Articlecreatejob;
use App\Jobs\Createitemsjob;
use App\Models\Book;
use App\Models\Convertdata;
use App\Models\Participer;
use App\Models\Sourcegroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Str;

class BookController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function store(Request $request)
    {
        $array =[];
        $sourcegroups = request('sourcegroups');
        $sourcegroup=Sourcegroup::firstOrCreate($sourcegroups);
        $i = 0;
        $time=now()->format('his');
        foreach($request->input('items') as $itemarticle){
            $i++;
            if(isset($itemarticle['json']) && $itemarticle['json'] && isset($itemarticle['json']['row']) && $itemarticle['json']['row']){
                if(str::slug($itemarticle['json']['row'][2])!=''){
                    $slug=str::slug($itemarticle['json']['row'][2]);
                }
                else
                {
                    $slug=str::substr($slug=str::slug($itemarticle['json']['row'][4]). $slug=str::slug($itemarticle['json']['row'][3]),0,50);
                }
                $array['sourcegroup_id']=$sourcegroup->id;
                $array['slug']=$itemarticle['json']['row'][1].'-'.$slug.'-'.$time.$i;
                $array['code']=$itemarticle['json']['row'][1];
                $array['title']=$itemarticle['json']['row'][2];
                $array['author']=$itemarticle['json']['row'][3];
                $array['year']=$itemarticle['json']['row'][4];
                $array['publisher']=$itemarticle['json']['row'][5];
                $array['folder_number']=$itemarticle['json']['row'][6];
                $array['act_number']=$itemarticle['json']['row'][7];
                $array['act_title']=$itemarticle['json']['row'][8];
                $array['chapter_number']=$itemarticle['json']['row'][9];
                $array['chapter_title']=$itemarticle['json']['row'][10];
                $array['search_number']=$itemarticle['json']['row'][11];
                $array['search_title']=$itemarticle['json']['row'][12];
                $array['request_number']=$itemarticle['json']['row'][13];
                $array['request_title']=$itemarticle['json']['row'][14];

                $array['secondary_title_one_number']=$itemarticle['json']['row'][15];
                $array['secondary_title_one']=$itemarticle['json']['row'][16];
                $array['secondary_title_two_number']=$itemarticle['json']['row'][17];
                $array['secondary_title_two']=$itemarticle['json']['row'][18];
                $array['page_number']=Convertdata::convertdata($itemarticle['json']['row'][19]);
                $array['source_number']=$itemarticle['json']['row'][20];
                $array['language_id']=$request->input('language_id');
                $array['comments']=$itemarticle['json']['row'][21];
                $array['created_at']=now();
                $array['updated_at']=now();
                $array['is_valid'] = 1;
                $groupitems[] = $array;
            }
        }
       $item =Createitemsjob::dispatch($groupitems,'books');

        if($item)
        return response(201);

        return response(403);
    }




}
