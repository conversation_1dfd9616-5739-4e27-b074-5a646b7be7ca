<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Articlecreatejob;
use App\Jobs\Createitemsjob;
use App\Models\Convertdata;
use App\Models\InterStandard;
use App\Models\Participer;
use App\Models\Sourcegroup;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class InterStandardController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $array = [];
        $sourcegroups = request('sourcegroups');
        $sourcegroup = Sourcegroup::firstOrCreate($sourcegroups);
        $i = 0;
        foreach ($request->input('items') as $itemarticle) {
            $i++;
            $time = now()->format('his');
            if (isset($itemarticle['json']) && $itemarticle['json'] && isset($itemarticle['json']['row']) && $itemarticle['json']['row']) {
                if (str::slug($itemarticle['json']['row'][2]) != '') {
                    $slug = str::slug($itemarticle['json']['row'][2]);
                } else {
                    $slug = str::substr($slug = str::slug($itemarticle['json']['row'][4]) . $slug = str::slug($itemarticle['json']['row'][3]), 0, 50);
                }
                $array['sourcegroup_id'] = $sourcegroup->id;
                $array['slug'] = $itemarticle['json']['row'][1] . '-' . $slug . '-' . $time . $i;;
                $array['code'] = $itemarticle['json']['row'][1];
                $array['author'] = $itemarticle['json']['row'][2];
                $array['type'] = $itemarticle['json']['row'][3];
                $array['serial_number'] = $itemarticle['json']['row'][4];
                $array['title'] = $itemarticle['json']['row'][5];
                $array['year'] = $itemarticle['json']['row'][6];
                $array['main_title_number'] = $itemarticle['json']['row'][7];
                $array['main_title'] = $itemarticle['json']['row'][8];

                $array['secondary_title_one_number'] = $itemarticle['json']['row'][9];
                $array['secondary_title_one'] = $itemarticle['json']['row'][10];
                $array['secondary_title_two_number'] = $itemarticle['json']['row'][11];
                $array['secondary_title_two'] = $itemarticle['json']['row'][12];
                $array['page_number'] = Convertdata::convertdata($itemarticle['json']['row'][13]);
                $array['source_number'] = $itemarticle['json']['row'][14];
                $array['language_id'] = $request->input('language_id');
                $array['comments'] = $itemarticle['json']['row'][15];

                $array['created_at'] = now();
                $array['updated_at'] = now();
                $array['is_valid'] = 1;
                $groupitems[] = $array;
            }
        }
        $item = Createitemsjob::dispatch($groupitems, 'inter_standards');

        if ($item)
            return response(201);

        return response(403);
    }
    public function store2(Request $request)
    {
        $array = [];
        $sourcegroups = request('sourcegroups');

        $array['code'] = request('code');
        $array['author'] = $request->input('author');
        $array['serial_number'] = $request->input('serial_number');
        $array['title'] = $request->input('title');
        $array['year'] = $request->input('year');
        $array['main_title_number'] = $request->input('main_title_number');
        $array['main_title'] = $request->input('main_title');

        $array['secondary_title_one_number'] = $request->input('secondary_title_one_number');
        $array['secondary_title_one'] = $request->input('secondary_title_one');
        $array['secondary_title_two_number'] = $request->input('secondary_title_two_number');
        $array['secondary_title_two'] = $request->input('secondary_title_two');
        $array['page_number'] = $request->input('page_number');
        $array['source_number'] = $request->input('source_number');
        $array['language_id'] = $request->input('language_id');
        $array['comments'] = $request->input('comments');




        $participers = request('participers');
        Articlecreatejob::dispatch($sourcegroups, $array, $participers, 'InterStandard');

        return response(201);
    }
}
