<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Articlecreatejob;
use App\Jobs\Createitemsjob;
use App\Models\Convertdata;
use App\Models\Participer;
use App\Models\Report;
use App\Models\Sourcegroup;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ReportController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $array = [];
        $sourcegroups = request('sourcegroups');

        $sourcegroup = Sourcegroup::firstOrCreate($sourcegroups);

        $i = 0;
        $time = now()->format('his');
        foreach ($request->input('items') as $itemarticle) {
            $i++;
            if (isset($itemarticle['json']) && $itemarticle['json'] && isset($itemarticle['json']['row']) && $itemarticle['json']['row']) {
                if (str::slug($itemarticle['json']['row'][2]) != '') {
                    $slug = str::slug($itemarticle['json']['row'][2]);
                } else {
                    $slug = str::substr($slug = str::slug($itemarticle['json']['row'][4]) . $slug = str::slug($itemarticle['json']['row'][3]), 0, 50);
                }
                $array['sourcegroup_id'] = $sourcegroup->id;
                $array['slug'] = $itemarticle['json']['row'][1] . '-' . $slug . '-' . $time . $i;

                $array['code'] = $itemarticle['json']['row'][0];
                $array['author'] = $itemarticle['json']['row'][1];
                $array['serial_number'] = $itemarticle['json']['row'][2];
                $array['title'] = $itemarticle['json']['row'][3];
                $array['year'] = $itemarticle['json']['row'][4];
                $array['act_number'] = $itemarticle['json']['row'][5];
                $array['act_title'] = $itemarticle['json']['row'][6];
                $array['chapter_number'] = $itemarticle['json']['row'][7];
                $array['chapter_title'] = $itemarticle['json']['row'][8];
                $array['section_number'] = $itemarticle['json']['row'][9];
                $array['section_title'] = $itemarticle['json']['row'][10];
                $array['secondary_title_one_number'] = $itemarticle['json']['row'][11];
                $array['secondary_title_one'] = $itemarticle['json']['row'][12];
                $array['secondary_title_two_number'] = $itemarticle['json']['row'][13];
                $array['secondary_title_two'] = $itemarticle['json']['row'][14];
                $array['page_number'] = Convertdata::convertdata($itemarticle['json']['row'][15]);
                $array['source_number'] = $itemarticle['json']['row'][16];
                $array['comments'] = $itemarticle['json']['row'][18];
                $array['created_at'] = now();
                $array['updated_at'] = now();
                $array['is_valid'] = 1;

                $groupitems[] = $array;


            }
        }
        $item = Createitemsjob::dispatch($groupitems, 'reports');

        if ($item)
            return response(201);

        return response(403);
    }

}
