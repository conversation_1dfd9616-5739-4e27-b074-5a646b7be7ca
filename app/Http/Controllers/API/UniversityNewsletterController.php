<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Imports\UniversityNewsletterImport;

use App\Jobs\Addarticlejobuniversitynewsletter;
use App\Jobs\Articlecreatejob;
use App\Jobs\Createitemsjob;
use App\Models\Convertdata;
use App\Models\Participer;
use App\Models\Sourcegroup;
use App\Models\UniversityNewsletter;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class UniversityNewsletterController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $array =[];
        $sourcegroups = request('sourcegroups');
        $sourcegroup=Sourcegroup::firstOrCreate($sourcegroups);
        $i = 0;
        foreach($request->input('items') as $itemarticle){

            $i++;
            if(isset($itemarticle['json']) && $itemarticle['json'] && isset($itemarticle['json']['row']) && $itemarticle['json']['row']){
                $array['sourcegroup_id']=$sourcegroup->id;
                $array['slug']=$itemarticle['json']['row'][0].'-'.$i.$itemarticle['json']['row'][9];
                $array['code']=$itemarticle['json']['row'][0];
                $array['title']=$itemarticle['json']['row'][1];
                $array['author']=$itemarticle['json']['row'][2];
                $array['year']=$itemarticle['json']['row'][3];
                $array['university']=$itemarticle['json']['row'][4];
                $array['folder_number']=$itemarticle['json']['row'][5];
                $array['act_number']=$itemarticle['json']['row'][6];
                $array['act_title']=$itemarticle['json']['row'][7];
                $array['chapter_number']=$itemarticle['json']['row'][8];
                $array['chapter_title']=$itemarticle['json']['row'][9];
                $array['search_number']=$itemarticle['json']['row'][10];
                $array['search_title']=$itemarticle['json']['row'][11];
                $array['request_number']=$itemarticle['json']['row'][12];
                $array['request_title']=$itemarticle['json']['row'][13];
                $array['secondary_title_one_number']=$itemarticle['json']['row'][14];
                $array['secondary_title_one']=$itemarticle['json']['row'][15];
                $array['secondary_title_two_number']=$itemarticle['json']['row'][16];
                $array['secondary_title_two']=$itemarticle['json']['row'][17];
                $array['page_number']=Convertdata::convertdata($itemarticle['json']['row'][18]);
                $array['source_number']=$itemarticle['json']['row'][19];
                $array['language_id']=$request->input('language_id');
                $array['comments']=$itemarticle['json']['row'][20];
                $array['created_at']=now();
                $array['updated_at']=now();
                $array['is_valid'] = 1;
                $groupitems[] = $array;
            }
        }
        $item =Createitemsjob::dispatch($groupitems,'university_newsletters');

        if($item)
        return response(201);

        return response(403);

    }

}
