<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Articlecreatejob;
use App\Jobs\Createitemsjob;
use App\Models\Convertdata;
use App\Models\LegalBody;
use App\Models\Participer;
use App\Models\Sourcegroup;
use Illuminate\Http\Request;
use Log;
use Illuminate\Support\Str;

class LegalBodyController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $array = [];
        $sourcegroups = request('sourcegroups');

        $array = [];
        $sourcegroup = Sourcegroup::firstOrCreate($sourcegroups);
        $i = 0;
        $time = now()->format('his');
        foreach ($request->input('items') as $itemarticle) {
            $i++;
            if (isset($itemarticle['json']) && $itemarticle['json'] && isset($itemarticle['json']['row']) && $itemarticle['json']['row']) {
                if (str::slug($itemarticle['json']['row'][2]) != '') {
                    $slug = str::slug($itemarticle['json']['row'][2]);
                } else {
                    $slug = str::substr($slug = str::slug($itemarticle['json']['row'][4]) . $slug = str::slug($itemarticle['json']['row'][3]), 0, 50);
                }
                $array['sourcegroup_id'] = $sourcegroup->id;
                $array['code'] = ($itemarticle['json']['row'][1]) ?? '';
                $array['author'] = $itemarticle['json']['row'][2];
                $array['slug'] = $itemarticle['json']['row'][1] . '-' . $slug . '-' . $time . $i;;
                $array['year'] = $itemarticle['json']['row'][3];
                $array['serial_number'] = $itemarticle['json']['row'][4];
                $array['secondary_number_one'] = $itemarticle['json']['row'][5];
                $array['secondary_number_two'] = $itemarticle['json']['row'][6];
                $array['group_title'] = $itemarticle['json']['row'][7];
                $array['title'] = $itemarticle['json']['row'][8];

                $array['secondary_title_one_number'] = $itemarticle['json']['row'][9];
                $array['secondary_title_one'] = $itemarticle['json']['row'][10];
                $array['secondary_title_two_number'] = $itemarticle['json']['row'][11];
                $array['secondary_title_two'] = $itemarticle['json']['row'][12];
                $array['page_number'] = Convertdata::convertdata($itemarticle['json']['row'][13]);
                $array['source_number'] = $itemarticle['json']['row'][14];
                $array['language_id'] = $request->input('language_id');
                $array['comments'] = $itemarticle['json']['row'][15];
                $array['created_at'] = now();
                $array['updated_at'] = now();
                $array['is_valid'] = 1;
                $groupitems[] = $array;
            }
        }

        $item = Createitemsjob::dispatch($groupitems, 'legal_bodies');

        if ($item)
            return response(201);

        return response(403);
    }
}
