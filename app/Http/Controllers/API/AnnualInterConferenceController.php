<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AnnualInterConference;
use App\Models\Participer;
use App\Models\Sourcegroup;

use App\Imports\AnnualInterConferenceImport;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Illuminate\Support\Facades\Http;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Jobs\Addarticlejob;
use App\Jobs\Articlecreatejob;
use App\Jobs\Createitemsjob;
use App\Models\Convertdata;
use App\Models\Filesource;
use Illuminate\Support\Str;

class AnnualInterConferenceController extends Controller
{

    public function store(Request $request)
    {
        $array = [];
        $sourcegroups = request('sourcegroups');
        $sourcegroup = Sourcegroup::firstOrCreate($sourcegroups);
        $i = 0;
        $time = now()->format('his');
        foreach ($request->input('items') as $itemarticle) {
            $i++;
            if (isset($itemarticle['json']) && $itemarticle['json'] && isset($itemarticle['json']['row']) && $itemarticle['json']['row']) {
                if (str::slug($itemarticle['json']['row'][2]) != '') {
                    $slug = str::slug($itemarticle['json']['row'][2]);
                } else {
                    $slug = str::substr($slug = str::slug($itemarticle['json']['row'][4]) . $slug = str::slug($itemarticle['json']['row'][3]), 0, 50);
                }
                $array['sourcegroup_id'] = $sourcegroup->id;
                $array['code'] = $itemarticle['json']['row'][0];
                $array['slug'] = $itemarticle['json']['row'][1] . '-' . $slug . '-' . $time . $i;;
                $array['organiser'] = $itemarticle['json']['row'][1];
                $array['course_number'] = $itemarticle['json']['row'][2];
                $array['conference_title'] = $itemarticle['json']['row'][3];
                $array['year'] = $itemarticle['json']['row'][4];
                $array['session_number'] = $itemarticle['json']['row'][5];
                $array['session_title'] = $itemarticle['json']['row'][6];
                $array['research_number'] = $itemarticle['json']['row'][7];
                $array['title'] = $itemarticle['json']['row'][8];
                $array['author'] = $itemarticle['json']['row'][9];
                $array['chapter_number'] = $itemarticle['json']['row'][10];
                $array['chapter_title'] = $itemarticle['json']['row'][11];
                $array['search_number'] = $itemarticle['json']['row'][12];
                $array['search_title'] = $itemarticle['json']['row'][13];
                $array['request_number'] = $itemarticle['json']['row'][14];
                $array['request_title'] = $itemarticle['json']['row'][15];
                $array['secondary_title_one_number'] = $itemarticle['json']['row'][16];
                $array['secondary_title_one'] = $itemarticle['json']['row'][17];
                $array['secondary_title_two_number'] = $itemarticle['json']['row'][18];
                $array['secondary_title_two'] = $itemarticle['json']['row'][19];
                $array['page_number'] = Convertdata::convertdata($itemarticle['json']['row'][20]);
                $array['source_number'] = $itemarticle['json']['row'][21];
                $array['language_id'] = $request->input('language_id');
                $array['comments'] = $itemarticle['json']['row'][22];
                $array['created_at'] = now();
                $array['updated_at'] = now();
                $array['is_valid'] = 1;
                $groupitems[] = $array;
            }
        }
        info(Convertdata::convertdata($itemarticle['json']['row'][20]));
        $item = Createitemsjob::dispatch($groupitems, 'annual_inter_conferences');

        if ($item)
            return response(201);

        return response(403);
    }
}
