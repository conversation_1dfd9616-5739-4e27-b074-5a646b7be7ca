<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Filesource;
use App\Models\Importarticle;
use Illuminate\Http\Request;

class ImportarticleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $filesources= Importarticle::all();
        return view('import.dataexcelfiles', ['filesources' => $filesources]);
    }

    public function updatedsourcefile(Request $request)
    {
      $file_source_id= $request->input('file_source_id');
      $filesource = Importarticle::where('id',$file_source_id)->first();
      if($filesource)
      $filesource->update(['is_updated'=>true]);
        $filename=$filesource->fileurl;
        $filename = explode('/',$filename);
        $name = end($filename);
      $excelfile = Filesource::where('filename',$name)->first();
      $excelfile->is_updated = true;
      $excelfile->update();
      return back()->with('success','updated');


    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $array =[];


        $array['modeltype']=request('modeltype');
        $array['fileurl']= request('fileurl');
 

        $item = Importarticle::create($array);
        
        return response($item,201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
