<?php

namespace App\Imports;

use App\Models\AnnualInterConference;
use App\Models\Sourcegroup;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\ToCollection;

use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMappedCells;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Validators\Failure;
use Throwable;

class AnnualInterConferenceImport implements   ToModel,WithBatchInserts,WithChunkReading,ShouldQueue,SkipsOnError,SkipsOnFailure,SkipsEmptyRows
{


    public function mapping(): array
    {
        return [
            'groupname'  => 'Q4',
            'groupnumber'  => 'AA4',
            'sourcetitle'  => 'Q5',
            'sourcetitlenumber'  => 'AA5',

        ];
    }

 
    
    public function model(array $row)
    {      
        $sourcegroup = new Sourcegroup();
       $sourcegroup->create([
            'groupname'                          => $row[4],
            'groupnumber'                      => $row[1],
            'sourcetitle'                      => $row[2],
            'sourcetitlenumber'                               => $row[3],

        ]);

        
        return new AnnualInterConference([

      //     'sourcegroup_id'        => $sourcegroup->id,
           'organiser'                          => $row['organiser'],
          'course_number'                      => $row['course_number'],
           'course_number'                      => $row['conference_title'],
            'year'                               => $row['year'],
        'session_number'                     => $row['session_number'],
           'session_title'                      => $row['session_title'],
           'research_number'                    => $row['research_number'],
            'title'                              => $row['title'],
            'author'                             => $row['author'],
           'chapter_number'                     => $row['chapter_number'],


           'chapter_title'                      => $row['chapter_title'],
           'search_number'                      => $row['search_number'],
           'search_title'                       => $row['search_title'],
           'request_number'                     => $row['request_number'],
           'request_title'                      => $row['request_title'],
           'secondary_title_one_number'         => $row['secondary_title_one_number'],
           'secondary_title_one'                => $row['secondary_title_one'],
           'secondary_title_two_number'         => $row['secondary_title_two_number'],
           'secondary_title_two'                => $row['secondary_title_two'],
           'page_number'                        => $row['page_number'],
           'source_number'                      => $row['source_number'],
           'comments'                           => $row['comments'] 
        ]);
    }

    public function onError(Throwable $e)
    {
        
    }
 
    public function rules(): array
    {
        
        return [
            '*.organiser' => ['text'],
            '*.course_number' => ['text'],
            '*.course_number' => ['text'],
            '*.year' => ['text'],
            '*.session_number' => ['text'],
            '*.session_title' => ['text'],
            '*.research_number' => ['text'],
            '*.title' => ['text'],
            '*.author' => ['text'],
            '*.chapter_number' => ['text'],
            '*.chapter_title' => ['text'],
            '*.search_number' => ['text'],
            '*.search_title' => ['text'],
            '*.request_number' => ['text'],
            '*.request_title' => ['text'],
            '*.secondary_title_one_number' => ['text'],
            '*.secondary_title_one' => ['text'],
            '*.secondary_title_two_number' => ['text'],
            '*.secondary_title_two' => ['text'],
            '*.page_number' => ['text'],
            '*.source_number' => ['text'],
            '*.comments' => ['text']
        ];
    }
    public function chunkSize(): int
    {
        return 1000;
    }
    public function batchSize():int
    {
        return 1000;
    }
    public function onFailure(Failure  ...$failure){

    }
/*
    public function headingRow(): int
    {
        return 8;
    }*/
    
}     










   /*
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            $sourcegroup = Sourcegroup::firstOrCreate([
                'groupname'                          => $row['groupname'],
                'groupnumber'                      => $row['groupnumber'],
                'sourcetitle'                      => $row['sourcetitle'],
                'sourcetitlenumber'                               => $row['sourcetitlenumber'],

            ]);

            $sourcegroup->annualinterconferences()->create([
                'sourcegroup_id'                     => $sourcegroup->id,
                'organiser'                          => $row['organiser'],
                'course_number'                      => $row['course_number'],
                'course_number'                      => $row['conference_title'],
                'year'                               => $row['year'],
                'session_number'                     => $row['session_number'],
                'session_title'                      => $row['session_title'],
                'research_number'                    => $row['research_number'],
                'title'                              => $row['title'],
                'author'                             => $row['author'],
                'chapter_number'                     => $row['chapter_number'],
                'chapter_title'                      => $row['chapter_title'],
                'search_number'                      => $row['search_number'],
                'search_title'                       => $row['search_title'],
                'request_number'                     => $row['request_number'],
                'request_title'                      => $row['request_title'],
                'secondary_title_one_number'         => $row['secondary_title_one_number'],
                'secondary_title_one'                => $row['secondary_title_one'],
                'secondary_title_two_number'         => $row['secondary_title_two_number'],
                'secondary_title_two'                => $row['secondary_title_two'],
                'page_number'                        => $row['page_number'],
                'source_number'                      => $row['source_number'],
                'comments'                           => $row['comments'] 
            ]);
        }
    }*/



/*       
    public function mapping(): array
    {
        return [
            'groupname'  => 'Q4',
            'groupnumber'  => 'AA4',
            'sourcetitle'  => 'Q5',
            'sourcetitlenumber'  => 'AA5',

        ];
    }





$sourcegrouparray=[
            'groupname'      => $row['groupname'],
            'groupnumber'     => $row['groupnumber'],
            'sourcetitle'     => $row['sourcetitle'],
            'sourcetitlenumber'     => $row['sourcetitlenumber'],
            ];
        $sourcegroup = Sourcegroup::firstOrCreate($sourcegrouparray);

        dd($sourcegroup);*/