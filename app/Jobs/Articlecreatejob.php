<?php

namespace App\Jobs;

use App\Models\Participer;
use App\Models\Sourcegroup;
use App\Models\UniversityNewsletter;
use App\Models\Research;
use App\Models\Magazine;
use Illuminate\Support\Facades\DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class Articlecreatejob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(private $sourcegroups,private $item,private $participers,private $modelgroup,private $mytable)
    {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        if(
        $this->sourcegroups && is_array($this->sourcegroups)
        && isset($this->sourcegroups['groupname']) && $this->sourcegroups['groupname']
        && isset($this->sourcegroups['groupnumber']) && $this->sourcegroups['groupnumber']
        && isset($this->sourcegroups['sourcetitle']) && $this->sourcegroups['sourcetitle']
        && isset($this->sourcegroups['sourcetitlenumber']) && $this->sourcegroups['sourcetitlenumber']
        )
        {$sourcegroup=Sourcegroup::firstOrCreate($this->sourcegroups);}

        if(isset($sourcegroup->id) && $sourcegroup->id ){

            $this->item['sourcegroup_id']=$sourcegroup->id;
           $item = DB::table($this->mytable)->insert($this->item);
           // $item = ('App\Models\\'.$this->modelgroup)::Create($this->item);

            return  $item;
        }

        return [];
    }
}
