<?php

namespace App\Jobs;

use App\Models\Filesource;
use App\Models\Modelgroup;
use App\Models\Sourcegroup;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class Createitemsjob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(private $items,private $mytable)
    {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            DB::table($this->mytable)->insert($this->items);
            }
                catch(\Exception $e){

                    $firstelement = ($this->items[0]) ?? [];
                    foreach($this->items as $item){
                        try{

                            DB::table($this->mytable)->insert($item);
                        }catch(\Exception $p){

                            $sourcegroup = Sourcegroup::where('id', '=', $item['sourcegroup_id'])->first();
                            $modelgroup = Modelgroup::where('class', '=', $sourcegroup->groupnumber)->first();
                            //dd($modelgroup);
                            $filesource = Filesource::where('modelgroups_id', '=', $modelgroup->id)->latest()->first();
                            $filesource->comment = $filesource->comment . ' <br> verification ligne de code :' . $item['code'] . ' feuille  : '.$item['source_number'];
                        // $filesource->exceldata = $filesource->exceldata-1;
                            $filesource->is_updated = true;
                            $filesource->is_error = true;
                            $filesource->save();
                        }
                    }



        }


    }
}
