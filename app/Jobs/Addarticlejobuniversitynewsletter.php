<?php

namespace App\Jobs;

use App\Models\AnnualInterConference;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class Addarticlejobuniversitynewsletter implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

     /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600000;


    public $elementarticle;
    public $jobtoken='eyJpdiI6ImJKNGUzaUhheWJLUDdTcnhTQ2YwMWc9PSIsInZhbHVlIjoiQi9sOXJXaGFubklhalJWQmVIWnIreG5PTSt3dURWelNJdWJFcDNBL25vQ1JSMG05Vm0rVnJvb1huUXZ3bUticVEyT1BhRERwdWlUampJV0NiYlZHWnE0MU1pOWNrRy82c2dMbG5MMktXSytxVHB0ZW5DSWlwWEt3Qm1MOUZ3TlMiLCJtYWMiOiIyNjg5MTQ5YWM4ODAwNTI3N2ZmYjk3NjNjZTY2YjJkODM2ZjU2ZTFjYmE3MzJmZTYxMzg0ZGU3MGJhNzRiMmRlIiwidGFnIjoiIn0';
    public $joburl='http://finance-islamique.test/api/universitynewsletters/store';
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($article)
    {   
        $this->elementarticle = $article;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
       $saved=  Http::retry(0, 1000)->accept('application/json')->withToken($this->jobtoken)
        ->post($this->joburl,  $this->elementarticle );  
    }
}
