<?php

namespace App\Console\Commands;

use App\Mail\ExampleMicrosoftGraphMail;
use App\Services\MicrosoftGraphMailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class DemoMicrosoftGraphMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:demo-microsoft-graph {email} {--message=Hello from Microsoft Graph!}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Demo Microsoft Graph mail service by sending a test email';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $message = $this->option('message');

        $this->info('Microsoft Graph Mail Service Demo');
        $this->line('================================');

        // Check if credentials are configured
        $service = app(MicrosoftGraphMailService::class);
        
        $this->info('1. Testing Microsoft Graph connection...');
        if (!$service->testConnection()) {
            $this->error('✗ Microsoft Graph connection failed');
            $this->line('');
            $this->line('Please ensure you have configured:');
            $this->line('- MICROSOFT_GRAPH_TENANT_ID');
            $this->line('- MICROSOFT_GRAPH_CLIENT_ID');
            $this->line('- MICROSOFT_GRAPH_CLIENT_SECRET');
            $this->line('- MAIL_FROM_ADDRESS (valid Office 365 email)');
            return 1;
        }

        $this->info('✓ Microsoft Graph connection successful');

        // Create test user
        $user = (object) [
            'name' => 'Demo User',
            'email' => $email
        ];

        $this->info('2. Creating mailable...');
        $mailable = new ExampleMicrosoftGraphMail($user, $message);
        $this->info('✓ Mailable created');

        $this->info('3. Sending email via Microsoft Graph...');
        try {
            Mail::mailer('microsoft-graph')
                ->to($email)
                ->send($mailable);

            $this->info('✓ Email sent successfully!');
            $this->line('');
            $this->line("Email sent to: {$email}");
            $this->line("Subject: Example Email via Microsoft Graph");
            $this->line("Message: {$message}");
            
            return 0;

        } catch (\Exception $e) {
            $this->error('✗ Failed to send email');
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
