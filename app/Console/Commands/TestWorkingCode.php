<?php

namespace App\Console\Commands;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Command;

class TestWorkingCode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:working-code {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test using the exact working code you provided';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info('Testing with your exact working code...');
        $this->line('=========================================');

        try {
            // Use your exact working code
            $token = $this->getMicrosoftAccessToken();
            
            if (!$token) {
                $this->error('Failed to get access token');
                return 1;
            }
            
            $this->info('✓ Access token obtained: ' . substr($token, 0, 50) . '...');

            $client = new Client(['base_uri' => 'https://graph.microsoft.com/']);
            $fromAddress = env('MAIL_FROM_ADDRESS');
            
            $this->info("Sending from: {$fromAddress}");
            $this->info("Sending to: {$email}");

            $mailcontent = [
                'subject' => 'Test Email from Working Code',
                'body' => '<h1>Test Email</h1><p>This email was sent using your exact working code.</p>'
            ];

            $response = $client->post("v1.0/users/$fromAddress/sendMail", [
                'headers' => [
                    'Authorization' => "Bearer $token",
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'message' => [
                        'subject' => $mailcontent['subject'],
                        'body' => [
                            'contentType' => 'HTML',
                            'content' => $mailcontent['body'],
                        ],
                        'toRecipients' => [
                            ['emailAddress' => ['address' => $email]],
                        ],
                    ],
                    'saveToSentItems' => true,
                ],
            ]);

            $statusCode = $response->getStatusCode();
            $this->info("Response status: {$statusCode}");

            if ($statusCode === 202) {
                $this->info('✓ Email sent successfully!');
                return 0;
            } else {
                $this->error("Unexpected status code: {$statusCode}");
                $this->line('Response body: ' . $response->getBody());
                return 1;
            }

        } catch (RequestException $e) {
            $this->error('Request failed: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $statusCode = $response->getStatusCode();
                $body = $response->getBody()->getContents();
                
                $this->line("Status Code: {$statusCode}");
                $this->line("Response Body: {$body}");
                
                // Parse the error
                $errorData = json_decode($body, true);
                if (isset($errorData['error'])) {
                    $this->line("Error Code: " . ($errorData['error']['code'] ?? 'Unknown'));
                    $this->line("Error Message: " . ($errorData['error']['message'] ?? 'Unknown'));
                }
            }
            
            return 1;
        }
    }

    /**
     * Your exact working getMicrosoftAccessToken method
     */
    private function getMicrosoftAccessToken()
    {
        $client = new Client();
        
        $tenantId = env('MICROSOFT_GRAPH_TENANT_ID');
        $clientId = env('MICROSOFT_GRAPH_CLIENT_ID');
        $clientSecret = env('MICROSOFT_GRAPH_CLIENT_SECRET');
        
        $this->line("Using Tenant ID: {$tenantId}");
        $this->line("Using Client ID: {$clientId}");
        $this->line("Using Client Secret: " . (empty($clientSecret) ? 'NOT SET' : 'SET'));
        
        try {
            $res = $client->post("https://login.microsoftonline.com/{$tenantId}/oauth2/v2.0/token", [
                'form_params' => [
                    'client_id' => $clientId,
                    'scope' => 'https://graph.microsoft.com/.default',
                    'client_secret' => $clientSecret,
                    'grant_type' => 'client_credentials',
                ]
            ]);
            
            $data = json_decode($res->getBody(), true);
            
            if (isset($data['access_token'])) {
                $this->line('Token expires in: ' . ($data['expires_in'] ?? 'unknown') . ' seconds');
                return $data['access_token'];
            } else {
                $this->error('No access token in response');
                $this->line('Response: ' . json_encode($data));
                return null;
            }
            
        } catch (RequestException $e) {
            $this->error('Token request failed: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $body = $response->getBody()->getContents();
                $this->line("Token Error Response: {$body}");
            }
            
            return null;
        }
    }
}
