<?php

namespace App\Console\Commands;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class VerifyMicrosoftGraphCredentials extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'microsoft-graph:verify-credentials';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify Microsoft Graph credentials directly';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Verifying Microsoft Graph Credentials');
        $this->line('=======================================');

        // Get credentials from environment
        $tenantId = env('MICROSOFT_GRAPH_TENANT_ID');
        $clientId = env('MICROSOFT_GRAPH_CLIENT_ID');
        $clientSecret = env('MICROSOFT_GRAPH_CLIENT_SECRET');

        $this->info('Tenant ID: ' . $tenantId);
        $this->info('Client ID: ' . $clientId);
        $this->info('Client Secret: ' . (empty($clientSecret) ? 'NOT SET' : 'SET'));

        if (empty($tenantId) || empty($clientId) || empty($clientSecret)) {
            $this->error('One or more credentials are missing!');
            return 1;
        }

        $this->info('Attempting to get token directly...');

        try {
            $client = new Client();
            $response = $client->post("https://login.microsoftonline.com/{$tenantId}/oauth2/v2.0/token", [
                'form_params' => [
                    'client_id' => $clientId,
                    'scope' => 'https://graph.microsoft.com/.default',
                    'client_secret' => $clientSecret,
                    'grant_type' => 'client_credentials',
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            
            if (isset($data['access_token'])) {
                $this->info('✓ Successfully obtained access token!');
                $this->line('Token type: ' . ($data['token_type'] ?? 'unknown'));
                $this->line('Expires in: ' . ($data['expires_in'] ?? 'unknown') . ' seconds');
                $this->line('');
                $this->info('Your Microsoft Graph credentials are valid.');
                return 0;
            } else {
                $this->error('✗ Failed to get access token. Response did not contain token.');
                $this->line('Response: ' . json_encode($data));
                return 1;
            }

        } catch (RequestException $e) {
            $this->error('✗ Request failed: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $body = json_decode($response->getBody(), true);
                
                $this->line('');
                $this->line('Error details:');
                $this->line('Status code: ' . $response->getStatusCode());
                
                if (isset($body['error'])) {
                    $this->line('Error: ' . $body['error']);
                }
                
                if (isset($body['error_description'])) {
                    $this->line('Description: ' . $body['error_description']);
                }
                
                // Provide guidance based on error
                if (isset($body['error']) && $body['error'] === 'unauthorized_client') {
                    $this->line('');
                    $this->line('This error typically means:');
                    $this->line('1. The application ID is incorrect or not registered in your tenant');
                    $this->line('2. The tenant ID is incorrect');
                    $this->line('3. The application has not been granted proper permissions');
                    $this->line('');
                    $this->line('Please check your Azure Portal to verify:');
                    $this->line('- The application exists in your tenant');
                    $this->line('- The application has been granted Mail.Send permission');
                    $this->line('- Admin consent has been granted for the application');
                }
            }
            
            return 1;
        } catch (\Exception $e) {
            $this->error('✗ General error: ' . $e->getMessage());
            return 1;
        }
    }
}
