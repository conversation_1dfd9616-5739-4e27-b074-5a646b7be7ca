<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SecurityMonitor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:monitor {--hours=24 : Hours to look back}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor security events and generate alerts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = $this->option('hours');
        $since = Carbon::now()->subHours($hours);
        
        $this->info("Security Monitor Report - Last {$hours} hours");
        $this->info("=" . str_repeat("=", 50));
        
        // Check for suspicious activities
        $this->checkFailedLogins($since);
        $this->checkRateLimitExceeded($since);
        $this->checkXSSAttempts($since);
        $this->checkSuspiciousPatterns($since);
        $this->checkHighFrequencyRequests($since);
        
        $this->info("\nMonitoring complete!");
    }

    private function checkFailedLogins($since)
    {
        $logFile = storage_path('logs/laravel.log');
        if (!file_exists($logFile)) {
            return;
        }

        $failedLogins = $this->searchLogFile($logFile, 'Unauthorized Nova access attempt', $since);
        
        if (count($failedLogins) > 0) {
            $this->warn("⚠️  Failed Login Attempts: " . count($failedLogins));
            
            // Group by IP
            $byIp = [];
            foreach ($failedLogins as $attempt) {
                $ip = $this->extractIpFromLog($attempt);
                $byIp[$ip] = ($byIp[$ip] ?? 0) + 1;
            }
            
            foreach ($byIp as $ip => $count) {
                if ($count > 5) {
                    $this->error("  🚨 High risk IP: {$ip} ({$count} attempts)");
                } else {
                    $this->line("  - IP: {$ip} ({$count} attempts)");
                }
            }
        } else {
            $this->info("✅ No failed login attempts detected");
        }
    }

    private function checkRateLimitExceeded($since)
    {
        $logFile = storage_path('logs/laravel.log');
        $rateLimitEvents = $this->searchLogFile($logFile, 'Nova API rate limit exceeded', $since);
        
        if (count($rateLimitEvents) > 0) {
            $this->warn("⚠️  Rate Limit Exceeded: " . count($rateLimitEvents) . " times");
            
            // Show top offenders
            $byIp = [];
            foreach ($rateLimitEvents as $event) {
                $ip = $this->extractIpFromLog($event);
                $byIp[$ip] = ($byIp[$ip] ?? 0) + 1;
            }
            
            arsort($byIp);
            $top5 = array_slice($byIp, 0, 5, true);
            
            foreach ($top5 as $ip => $count) {
                $this->line("  - IP: {$ip} ({$count} times)");
            }
        } else {
            $this->info("✅ No rate limit violations detected");
        }
    }

    private function checkXSSAttempts($since)
    {
        $logFile = storage_path('logs/laravel.log');
        $xssAttempts = $this->searchLogFile($logFile, 'XSS attempt detected', $since);
        
        if (count($xssAttempts) > 0) {
            $this->error("🚨 XSS Attempts Detected: " . count($xssAttempts));
            
            foreach (array_slice($xssAttempts, 0, 5) as $attempt) {
                $this->line("  - " . substr($attempt, 0, 100) . "...");
            }
        } else {
            $this->info("✅ No XSS attempts detected");
        }
    }

    private function checkSuspiciousPatterns($since)
    {
        $logFile = storage_path('logs/laravel.log');
        $suspiciousEvents = $this->searchLogFile($logFile, 'Suspicious pattern detected', $since);
        
        if (count($suspiciousEvents) > 0) {
            $this->error("🚨 Suspicious Patterns Detected: " . count($suspiciousEvents));
            
            foreach (array_slice($suspiciousEvents, 0, 3) as $event) {
                $this->line("  - " . substr($event, 0, 100) . "...");
            }
        } else {
            $this->info("✅ No suspicious patterns detected");
        }
    }

    private function checkHighFrequencyRequests($since)
    {
        $logFile = storage_path('logs/laravel.log');
        $highFreqEvents = $this->searchLogFile($logFile, 'High frequency Nova requests detected', $since);
        
        if (count($highFreqEvents) > 0) {
            $this->warn("⚠️  High Frequency Requests: " . count($highFreqEvents));
        } else {
            $this->info("✅ No high frequency request patterns detected");
        }
    }

    private function searchLogFile($file, $pattern, $since)
    {
        $results = [];
        $handle = fopen($file, 'r');
        
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                if (strpos($line, $pattern) !== false) {
                    // Extract timestamp and check if it's within our timeframe
                    if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $matches)) {
                        $logTime = Carbon::createFromFormat('Y-m-d H:i:s', $matches[1]);
                        if ($logTime->gte($since)) {
                            $results[] = $line;
                        }
                    }
                }
            }
            fclose($handle);
        }
        
        return $results;
    }

    private function extractIpFromLog($logLine)
    {
        if (preg_match('/"ip":"([^"]+)"/', $logLine, $matches)) {
            return $matches[1];
        }
        return 'unknown';
    }
}
