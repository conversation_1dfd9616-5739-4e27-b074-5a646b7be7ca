<?php

namespace App\Console\Commands;

use App\Services\MicrosoftGraphMailService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Command;

class DiagnoseMicrosoftGraphPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'microsoft-graph:diagnose-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose Microsoft Graph permissions and provide guidance';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Microsoft Graph Permissions Diagnosis');
        $this->line('=====================================');

        // Check configuration
        $tenantId = config('services.microsoft_graph.tenant_id');
        $clientId = config('services.microsoft_graph.client_id');
        $clientSecret = config('services.microsoft_graph.client_secret');
        $fromAddress = config('mail.from.address');

        $this->info('Configuration Check:');
        $this->line("Tenant ID: {$tenantId}");
        $this->line("Client ID: {$clientId}");
        $this->line("Client Secret: " . (empty($clientSecret) ? 'NOT SET' : 'SET'));
        $this->line("From Address: {$fromAddress}");
        $this->line('');

        // Test token acquisition
        $service = app(MicrosoftGraphMailService::class);
        $token = $service->getAccessToken();

        if (!$token) {
            $this->error('❌ Cannot get access token');
            return 1;
        }

        $this->info('✅ Access token obtained successfully');
        $this->line('');

        // Test permissions by trying different endpoints
        $this->info('Testing Permissions:');
        
        $client = new Client(['base_uri' => 'https://graph.microsoft.com/']);
        
        // Test 1: Try to access application info
        try {
            $response = $client->get('v1.0/applications', [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type' => 'application/json',
                ]
            ]);
            $this->info('✅ Application.Read.All permission: OK');
        } catch (RequestException $e) {
            $this->line('❌ Application.Read.All permission: Missing or denied');
        }

        // Test 2: Try to access users (requires User.Read.All)
        try {
            $response = $client->get('v1.0/users?$top=1', [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type' => 'application/json',
                ]
            ]);
            $this->info('✅ User.Read.All permission: OK');
        } catch (RequestException $e) {
            $this->line('❌ User.Read.All permission: Missing or denied');
        }

        // Test 3: Try to send a test email (this will likely fail)
        $this->line('');
        $this->info('Testing Mail.Send permission:');
        
        try {
            $testEmailData = [
                'message' => [
                    'subject' => 'Test Permission Check',
                    'body' => [
                        'contentType' => 'Text',
                        'content' => 'This is a test email to check permissions.',
                    ],
                    'toRecipients' => [
                        ['emailAddress' => ['address' => $fromAddress]],
                    ],
                ],
                'saveToSentItems' => false,
            ];

            $response = $client->post("v1.0/users/{$fromAddress}/sendMail", [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type' => 'application/json',
                ],
                'json' => $testEmailData
            ]);

            if ($response->getStatusCode() === 202) {
                $this->info('✅ Mail.Send permission: OK - Test email sent successfully!');
                return 0;
            }

        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $body = json_decode($response->getBody(), true);
                
                if (isset($body['error']['code'])) {
                    $errorCode = $body['error']['code'];
                    $errorMessage = $body['error']['message'] ?? 'Unknown error';
                    
                    $this->error("❌ Mail.Send permission failed: {$errorCode}");
                    $this->line("   Message: {$errorMessage}");
                    
                    // Provide specific guidance based on error
                    $this->line('');
                    $this->info('🔧 Required Actions:');
                    
                    if ($errorCode === 'ErrorAccessDenied' || $errorCode === 'Forbidden') {
                        $this->line('1. Go to Azure Portal → App registrations → Your app');
                        $this->line('2. Click "API permissions"');
                        $this->line('3. Add "Microsoft Graph" → "Application permissions" → "Mail.Send"');
                        $this->line('4. Click "Grant admin consent for your organization" (CRITICAL!)');
                        $this->line('5. Wait 5-10 minutes for permissions to propagate');
                    } elseif ($errorCode === 'ErrorInvalidUser') {
                        $this->line("1. The email address '{$fromAddress}' doesn't exist in your tenant");
                        $this->line('2. Use a valid Office 365 email address in MAIL_FROM_ADDRESS');
                        $this->line('3. Or create this user/mailbox in Office 365 Admin Center');
                    }
                }
            }
        }

        return 1;
    }
}
