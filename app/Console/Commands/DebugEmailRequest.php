<?php

namespace App\Console\Commands;

use App\Services\MicrosoftGraphMailService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Command;

class DebugEmailRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:email-request {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug the exact request being sent to Microsoft Graph';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info('Debugging Email Request');
        $this->line('======================');

        $service = app(MicrosoftGraphMailService::class);
        $token = $service->getAccessToken();

        if (!$token) {
            $this->error('Failed to get access token');
            return 1;
        }

        $fromAddress = config('mail.from.address');
        
        $this->info('Request Details:');
        $this->line("URL: https://graph.microsoft.com/v1.0/users/{$fromAddress}/sendMail");
        $this->line("Token (first 50 chars): " . substr($token, 0, 50) . "...");
        $this->line("From Address: {$fromAddress}");
        $this->line("To Address: {$email}");
        $this->line('');

        // Prepare the exact request data
        $headers = [
            'Authorization' => "Bearer {$token}",
            'Content-Type' => 'application/json',
        ];

        $requestData = [
            'message' => [
                'subject' => 'Debug Test Email',
                'body' => [
                    'contentType' => 'HTML',
                    'content' => '<h1>Debug Test</h1><p>This is a debug test email.</p>',
                ],
                'toRecipients' => [
                    ['emailAddress' => ['address' => $email]],
                ],
            ],
            'saveToSentItems' => true,
        ];

        $this->info('Headers being sent:');
        foreach ($headers as $key => $value) {
            if ($key === 'Authorization') {
                $this->line("  {$key}: Bearer " . substr($value, 7, 50) . "...");
            } else {
                $this->line("  {$key}: {$value}");
            }
        }

        $this->line('');
        $this->info('Request body:');
        $this->line(json_encode($requestData, JSON_PRETTY_PRINT));
        $this->line('');

        try {
            $client = new Client(['base_uri' => 'https://graph.microsoft.com/']);
            
            $this->info('Sending request...');
            
            $response = $client->post("v1.0/users/{$fromAddress}/sendMail", [
                'headers' => $headers,
                'json' => $requestData
            ]);

            $statusCode = $response->getStatusCode();
            $this->info("✅ Success! Status Code: {$statusCode}");
            
            if ($response->getBody()) {
                $this->line('Response Body: ' . $response->getBody());
            }

            return 0;

        } catch (RequestException $e) {
            $this->error('❌ Request Failed: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $statusCode = $response->getStatusCode();
                $body = $response->getBody()->getContents();
                
                $this->line("Status Code: {$statusCode}");
                $this->line("Response Body: {$body}");
                
                // Check if it's specifically a permissions issue
                $errorData = json_decode($body, true);
                if (isset($errorData['error']['code'])) {
                    $errorCode = $errorData['error']['code'];
                    
                    if ($errorCode === 'ErrorAccessDenied' || $errorCode === 'Forbidden') {
                        $this->line('');
                        $this->error('🔒 This is definitely a permissions issue!');
                        $this->line('The token is being sent correctly, but Azure is rejecting it.');
                        $this->line('You need to add Mail.Send permission and grant admin consent.');
                    } elseif ($errorCode === 'ErrorInvalidUser') {
                        $this->line('');
                        $this->error('👤 Invalid user issue!');
                        $this->line("The email address '{$fromAddress}' doesn't exist in your tenant.");
                    }
                }
            }
            
            return 1;
        }
    }
}
