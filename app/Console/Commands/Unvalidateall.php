<?php

namespace App\Console\Commands;

use Artisan;
use DB;
use Illuminate\Console\Command;

class Unvalidateall extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'unvalidate:all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::table('annual_inter_conferences')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('annual_inter_conferences')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('books')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('books')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('inter_conferences')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('inter_conferences')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('inter_conference_recommendations')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('inter_conference_recommendations')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('inter_standards')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('inter_standards')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('jurisprudences')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('jurisprudences')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('legal_bodies')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('legal_bodies')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('magazines')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('magazines')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('regulations')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('regulations')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('reports')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('reports')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('researches')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('researches')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        DB::table('university_newsletters')->orderBy('id')->lazy()->each(function ($book) {
            DB::table('university_newsletters')
            ->where('id', $book->id)
            ->update(['is_valid' => 0]);
        });
        Artisan::call("scout:import");
        return Command::SUCCESS;
    }
}
