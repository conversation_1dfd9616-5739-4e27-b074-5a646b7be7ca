<?php

namespace App\Console\Commands;

use App\Services\MicrosoftGraphMailService;
use Illuminate\Console\Command;

class TestMicrosoftGraphConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:test-microsoft-graph';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Microsoft Graph mail service connection';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing Microsoft Graph connection...');

        $service = app(MicrosoftGraphMailService::class);

        // Test getting access token
        $this->info('Attempting to get access token...');
        $token = $service->getAccessToken();

        if (!$token) {
            $this->error('Failed to get access token. Please check your configuration.');
            $this->line('');
            $this->line('Required environment variables:');
            $this->line('- MICROSOFT_GRAPH_TENANT_ID');
            $this->line('- MICROSOFT_GRAPH_CLIENT_ID');
            $this->line('- MICROSOFT_GRAPH_CLIENT_SECRET');
            return 1;
        }

        $this->info('✓ Access token obtained successfully');

        // Test connection
        $this->info('Testing API connection...');
        if ($service->testConnection()) {
            $this->info('✓ Microsoft Graph connection successful!');
            $this->line('');
            $this->info('Your Microsoft Graph mail service is configured correctly.');
            $this->line('You can now use MAIL_MAILER=microsoft-graph in your .env file.');
            return 0;
        } else {
            $this->error('✗ Microsoft Graph connection failed');
            $this->line('');
            $this->line('Please check:');
            $this->line('1. Your Azure app registration permissions');
            $this->line('2. Admin consent has been granted');
            $this->line('3. The application has Mail.Send permission');
            return 1;
        }
    }
}
