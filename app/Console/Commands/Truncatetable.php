<?php

namespace App\Console\Commands;

use App\Models\AnnualInterConference;
use App\Models\Book;
use App\Models\Filesource;
use App\Models\InterConference;
use App\Models\InterConferenceRecommendation;
use App\Models\InterStandard;
use App\Models\Jurisprudence;
use App\Models\LegalBody;
use App\Models\Magazine;
use App\Models\Participer;
use App\Models\Regulation;
use App\Models\Report;
use App\Models\Research;
use App\Models\Sourcegroup;
use App\Models\UniversityNewsletter;
use App\Models\User;
use Illuminate\Console\Command;

class Truncatetable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'financedata:truncate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        AnnualInterConference::truncate();
        Book::truncate();
        Filesource::truncate();
        InterConference::truncate();
        InterConferenceRecommendation::truncate();
        InterStandard::truncate();
        Jurisprudence::truncate();
        LegalBody::truncate();
        Magazine::truncate();
        Participer::truncate();
        Regulation::truncate();
        Report::truncate();
        Research::truncate();
        Sourcegroup::truncate();
        UniversityNewsletter::truncate();
        return 0;
    }
}
