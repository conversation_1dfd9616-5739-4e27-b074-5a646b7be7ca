<?php

namespace App\Console\Commands;

use App\Services\MicrosoftGraphMailService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Command;

class TestEmailAddress extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'microsoft-graph:test-email {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test if an email address is valid in Microsoft Graph';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Testing email address: {$email}");
        $this->line('================================');

        $service = app(MicrosoftGraphMailService::class);
        $token = $service->getAccessToken();

        if (!$token) {
            $this->error('Failed to get access token');
            return 1;
        }

        try {
            $client = new Client(['base_uri' => 'https://graph.microsoft.com/']);
            
            // Try to get user info for this email
            $response = $client->get("v1.0/users/{$email}", [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type' => 'application/json',
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                $this->info("✓ Email address '{$email}' is valid and exists in your tenant!");
                $this->line('You can use this email address as MAIL_FROM_ADDRESS in your .env file');
                return 0;
            }

        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $statusCode = $response->getStatusCode();
                
                if ($statusCode === 404) {
                    $this->error("✗ Email address '{$email}' does not exist in your tenant");
                    $this->line('Try a different email address that exists in your Office 365 tenant');
                } elseif ($statusCode === 403) {
                    $this->error("✗ Permission denied - cannot verify email address");
                    $this->line('Your app may need additional permissions, but you can still try using this email');
                } else {
                    $this->error("✗ Error {$statusCode}: " . $e->getMessage());
                }
            } else {
                $this->error("✗ Network error: " . $e->getMessage());
            }
            
            return 1;
        }

        return 1;
    }
}
