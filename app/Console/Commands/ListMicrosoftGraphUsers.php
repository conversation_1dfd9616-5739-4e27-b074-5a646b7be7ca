<?php

namespace App\Console\Commands;

use App\Services\MicrosoftGraphMailService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Command;

class ListMicrosoftGraphUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'microsoft-graph:list-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List available users in Microsoft Graph tenant';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Listing Microsoft Graph Users');
        $this->line('==============================');

        $service = app(MicrosoftGraphMailService::class);
        $token = $service->getAccessToken();

        if (!$token) {
            $this->error('Failed to get access token');
            return 1;
        }

        try {
            $client = new Client(['base_uri' => 'https://graph.microsoft.com/']);
            
            $response = $client->get('v1.0/users', [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type' => 'application/json',
                ],
                'query' => [
                    '$select' => 'displayName,mail,userPrincipalName,accountEnabled'
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            
            if (isset($data['value']) && is_array($data['value'])) {
                $this->info('Available users in your tenant:');
                $this->line('');
                
                foreach ($data['value'] as $user) {
                    $displayName = $user['displayName'] ?? 'N/A';
                    $mail = $user['mail'] ?? 'N/A';
                    $userPrincipalName = $user['userPrincipalName'] ?? 'N/A';
                    $enabled = $user['accountEnabled'] ?? false;
                    
                    $status = $enabled ? '✓' : '✗';
                    
                    $this->line("{$status} {$displayName}");
                    $this->line("   Email: {$mail}");
                    $this->line("   UPN: {$userPrincipalName}");
                    $this->line('');
                }
                
                $this->info('Use one of the valid email addresses above as MAIL_FROM_ADDRESS in your .env file');
                
            } else {
                $this->error('No users found or unexpected response format');
                $this->line('Response: ' . json_encode($data));
            }

            return 0;

        } catch (RequestException $e) {
            $this->error('Failed to list users: ' . $e->getMessage());
            
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $body = json_decode($response->getBody(), true);
                
                $this->line('Error details:');
                $this->line('Status: ' . $response->getStatusCode());
                
                if (isset($body['error']['message'])) {
                    $this->line('Message: ' . $body['error']['message']);
                }
                
                if ($response->getStatusCode() === 403) {
                    $this->line('');
                    $this->line('This error suggests your application needs User.Read.All permission.');
                    $this->line('Add this permission in Azure Portal and grant admin consent.');
                }
            }
            
            return 1;
        }
    }
}
