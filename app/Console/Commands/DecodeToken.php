<?php

namespace App\Console\Commands;

use App\Services\MicrosoftGraphMailService;
use Illuminate\Console\Command;

class DecodeToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'microsoft-graph:decode-token';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Decode the Microsoft Graph token to see what permissions it has';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Decoding Microsoft Graph Token');
        $this->line('==============================');

        $service = app(MicrosoftGraphMailService::class);
        $token = $service->getAccessToken();

        if (!$token) {
            $this->error('Failed to get access token');
            return 1;
        }

        // JWT tokens have 3 parts separated by dots
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            $this->error('Invalid JWT token format');
            return 1;
        }

        // Decode the payload (second part)
        $payload = $parts[1];
        
        // Add padding if needed
        $payload .= str_repeat('=', (4 - strlen($payload) % 4) % 4);
        
        $decoded = base64_decode($payload);
        $tokenData = json_decode($decoded, true);

        if (!$tokenData) {
            $this->error('Failed to decode token payload');
            return 1;
        }

        $this->info('Token Information:');
        $this->line('Issuer: ' . ($tokenData['iss'] ?? 'Unknown'));
        $this->line('Audience: ' . ($tokenData['aud'] ?? 'Unknown'));
        $this->line('Application ID: ' . ($tokenData['appid'] ?? 'Unknown'));
        $this->line('Tenant ID: ' . ($tokenData['tid'] ?? 'Unknown'));
        
        if (isset($tokenData['exp'])) {
            $expiry = date('Y-m-d H:i:s', $tokenData['exp']);
            $this->line("Expires: {$expiry}");
        }

        // Most importantly, check the roles/scopes
        $this->line('');
        $this->info('Permissions/Roles in Token:');
        
        if (isset($tokenData['roles']) && is_array($tokenData['roles'])) {
            foreach ($tokenData['roles'] as $role) {
                $this->line("✓ {$role}");
            }
            
            // Check if Mail.Send is present
            if (in_array('Mail.Send', $tokenData['roles'])) {
                $this->info('✅ Mail.Send permission is present in token');
            } else {
                $this->error('❌ Mail.Send permission is MISSING from token');
                $this->line('This confirms the permission is not granted in Azure Portal');
            }
        } else {
            $this->error('❌ No roles/permissions found in token');
            $this->line('This means no application permissions have been granted');
        }

        if (isset($tokenData['scp'])) {
            $this->line('Scopes: ' . $tokenData['scp']);
        }

        return 0;
    }
}
