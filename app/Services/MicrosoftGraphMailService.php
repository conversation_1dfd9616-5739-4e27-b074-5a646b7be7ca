<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class MicrosoftGraphMailService
{
    protected $client;
    protected $tenantId;
    protected $clientId;
    protected $clientSecret;
    protected $baseUri = 'https://graph.microsoft.com/';

    public function __construct()
    {
        $this->client = new Client(['base_uri' => $this->baseUri]);
        $this->tenantId = config('services.microsoft_graph.tenant_id');
        $this->clientId = config('services.microsoft_graph.client_id');
        $this->clientSecret = config('services.microsoft_graph.client_secret');
    }

    /**
     * Get Microsoft Graph access token using client credentials flow
     *
     * @return string|null
     */
    public function getAccessToken(): ?string
    {
        $cacheKey = 'microsoft_graph_access_token';
        
        // Try to get token from cache first
        $cachedToken = Cache::get($cacheKey);
        if ($cachedToken) {
            return $cachedToken;
        }

        try {
            $response = $this->client->post("https://login.microsoftonline.com/{$this->tenantId}/oauth2/v2.0/token", [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'scope' => 'https://graph.microsoft.com/.default',
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'client_credentials',
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            $accessToken = $data['access_token'] ?? null;

            if ($accessToken) {
                // Cache token for 55 minutes (tokens expire in 60 minutes)
                $expiresIn = ($data['expires_in'] ?? 3600) - 300; // 5 minutes buffer
                Cache::put($cacheKey, $accessToken, $expiresIn);
            }

            return $accessToken;

        } catch (RequestException $e) {
            Log::error('Failed to get Microsoft Graph access token', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);
            return null;
        }
    }

    /**
     * Send email using Microsoft Graph API
     *
     * @param array $emailData
     * @return bool
     */
    public function sendEmail(array $emailData): bool
    {
        $token = $this->getAccessToken();

        if (!$token) {
            Log::error('Cannot send email: Failed to get access token');
            return false;
        }

        $fromAddress = $emailData['from'] ?? config('mail.from.address');

        try {
            $response = $this->client->post("v1.0/users/{$fromAddress}/sendMail", [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type' => 'application/json',
                ],
                'json' => $this->formatEmailData($emailData)
            ]);

            Log::info('Email sent successfully via Microsoft Graph', [
                'from' => $emailData['from'] ?? config('mail.from.address'),
                'to' => $emailData['to'] ?? [],
                'subject' => $emailData['subject'] ?? 'No subject'
            ]);

            return $response->getStatusCode() === 202;

        } catch (RequestException $e) {
            Log::error('Failed to send email via Microsoft Graph', [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'email_data' => $emailData
            ]);
            return false;
        }
    }

    /**
     * Format email data for Microsoft Graph API
     *
     * @param array $emailData
     * @return array
     */
    protected function formatEmailData(array $emailData): array
    {
        $message = [
            'subject' => $emailData['subject'] ?? '',
            'body' => [
                'contentType' => $emailData['body_type'] ?? 'HTML',
                'content' => $emailData['body'] ?? '',
            ],
            'toRecipients' => $this->formatRecipients($emailData['to'] ?? []),
        ];

        // Add CC recipients if provided
        if (!empty($emailData['cc'])) {
            $message['ccRecipients'] = $this->formatRecipients($emailData['cc']);
        }

        // Add BCC recipients if provided
        if (!empty($emailData['bcc'])) {
            $message['bccRecipients'] = $this->formatRecipients($emailData['bcc']);
        }

        // Add reply-to if provided
        if (!empty($emailData['reply_to'])) {
            $message['replyTo'] = $this->formatRecipients($emailData['reply_to']);
        }

        // Add attachments if provided
        if (!empty($emailData['attachments'])) {
            $message['attachments'] = $this->formatAttachments($emailData['attachments']);
        }

        return [
            'message' => $message,
            'saveToSentItems' => $emailData['save_to_sent_items'] ?? true,
        ];
    }

    /**
     * Format recipients for Microsoft Graph API
     *
     * @param array $recipients
     * @return array
     */
    protected function formatRecipients(array $recipients): array
    {
        $formatted = [];

        foreach ($recipients as $recipient) {
            if (is_string($recipient)) {
                $formatted[] = ['emailAddress' => ['address' => $recipient]];
            } elseif (is_array($recipient) && isset($recipient['email'])) {
                $emailAddress = ['address' => $recipient['email']];
                if (!empty($recipient['name'])) {
                    $emailAddress['name'] = $recipient['name'];
                }
                $formatted[] = ['emailAddress' => $emailAddress];
            }
        }

        return $formatted;
    }

    /**
     * Format attachments for Microsoft Graph API
     *
     * @param array $attachments
     * @return array
     */
    protected function formatAttachments(array $attachments): array
    {
        $formatted = [];

        foreach ($attachments as $attachment) {
            if (isset($attachment['content']) && isset($attachment['name'])) {
                $formatted[] = [
                    '@odata.type' => '#microsoft.graph.fileAttachment',
                    'name' => $attachment['name'],
                    'contentType' => $attachment['content_type'] ?? 'application/octet-stream',
                    'contentBytes' => base64_encode($attachment['content'])
                ];
            }
        }

        return $formatted;
    }

    /**
     * Test the Microsoft Graph connection
     *
     * @return bool
     */
    public function testConnection(): bool
    {
        // For now, just check if we can get a token
        // This is enough to verify basic authentication
        $token = $this->getAccessToken();
        return !empty($token);
    }
}
