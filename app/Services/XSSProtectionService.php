<?php

namespace App\Services;

use HTMLPurifier;
use HTMLPurifier_Config;
use Illuminate\Support\Facades\Log;

class XSSProtectionService
{
    /**
     * HTML Purifier instance
     */
    private static $purifier = null;

    /**
     * Dangerous HTML tags that should be stripped
     */
    const DANGEROUS_TAGS = [
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
        'textarea', 'select', 'option', 'link', 'meta', 'style', 'base',
        'applet', 'bgsound', 'blink', 'body', 'frame', 'frameset',
        'head', 'html', 'ilayer', 'layer', 'title', 'xml'
    ];

    /**
     * Dangerous attributes that should be stripped
     */
    const DANGEROUS_ATTRIBUTES = [
        'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
        'onkeydown', 'onkeyup', 'onkeypress', 'onfocus', 'onblur',
        'onchange', 'onsubmit', 'onreset', 'onselect', 'onunload',
        'onabort', 'ondblclick', 'onmousedown', 'onmouseup', 'onmousemove',
        'oncontextmenu', 'ondrag', 'ondrop', 'onscroll', 'onresize',
        'javascript:', 'vbscript:', 'data:', 'expression'
    ];

    /**
     * XSS patterns to detect and block
     */
    const XSS_PATTERNS = [
        '/<script[^>]*>.*?<\/script>/is',
        '/<iframe[^>]*>.*?<\/iframe>/is',
        '/javascript:/i',
        '/vbscript:/i',
        '/on\w+\s*=/i',
        '/<\s*script/i',
        '/expression\s*\(/i',
        '/<\s*object/i',
        '/<\s*embed/i',
        '/<\s*applet/i',
        '/<\s*meta/i',
        '/<\s*link/i',
        '/data:text\/html/i',
        '/data:application\/javascript/i'
    ];

    /**
     * Get HTML Purifier instance
     *
     * @return HTMLPurifier
     */
    private static function getPurifier(): HTMLPurifier
    {
        // Always recreate purifier to ensure fresh configuration
        // if (self::$purifier === null) {
            $config = HTMLPurifier_Config::createDefault();
            
            // Configure allowed HTML tags and attributes - include span to fix RemoveSpansWithoutAttributes error
            $config->set('HTML.Allowed', config('xss_protection.allowed_html', 'p,br,strong,em,u,ol,ul,li,a[href],img[src|alt|width|height],span'));
            $config->set('HTML.SafeIframe', true);
            $config->set('URI.SafeIframeRegexp', '%^(https?:)?//(www\.youtube\.com/embed/|player\.vimeo\.com/video/)%');
            $config->set('HTML.Nofollow', true);
            $config->set('HTML.TargetBlank', true);
            $config->set('AutoFormat.RemoveEmpty', true);
            $config->set('AutoFormat.RemoveSpansWithoutAttributes', false); // Disabled to prevent configuration errors
            $config->set('AutoFormat.Linkify', false);
            $config->set('HTML.TidyLevel', 'heavy');

            self::$purifier = new HTMLPurifier($config);
        // }

        return self::$purifier;
    }

    /**
     * Clean HTML content using HTML Purifier
     *
     * @param string $content
     * @return string
     */
    public static function cleanHtml(string $content): string
    {
        if (empty($content)) {
            return $content;
        }

        try {
            return self::getPurifier()->purify($content);
        } catch (\Exception $e) {
            Log::warning('XSS Protection: HTML Purifier failed', [
                'content' => substr($content, 0, 200),
                'error' => $e->getMessage()
            ]);
            
            // Fallback to basic cleaning
            return self::basicClean($content);
        }
    }

    /**
     * Basic XSS cleaning without HTML Purifier
     *
     * @param string $content
     * @return string
     */
    public static function basicClean(string $content): string
    {
        if (empty($content)) {
            return $content;
        }

        // Remove dangerous patterns
        foreach (self::XSS_PATTERNS as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        // Remove dangerous attributes
        foreach (self::DANGEROUS_ATTRIBUTES as $attr) {
            $content = preg_replace('/\s*' . preg_quote($attr, '/') . '\s*=\s*["\'][^"\']*["\']/i', '', $content);
        }

        // Strip dangerous tags
        $content = strip_tags($content, config('xss_protection.allowed_tags', '<p><br><strong><em><u><ol><ul><li><a><img>'));

        return $content;
    }

    /**
     * Sanitize user input for safe storage
     *
     * @param mixed $input
     * @param array $options
     * @return mixed
     */
    public static function sanitizeInput($input, array $options = [])
    {
        if (is_array($input)) {
            return array_map(function ($item) use ($options) {
                return self::sanitizeInput($item, $options);
            }, $input);
        }

        if (!is_string($input)) {
            return $input;
        }

        $allowHtml = $options['allow_html'] ?? false;
        $strictMode = $options['strict_mode'] ?? false;

        if ($strictMode) {
            // Strip all HTML tags and decode entities
            return htmlspecialchars(strip_tags($input), ENT_QUOTES | ENT_HTML5, 'UTF-8');
        }

        if ($allowHtml) {
            return self::cleanHtml($input);
        }

        // Default: escape HTML entities
        return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    /**
     * Detect potential XSS in content
     *
     * @param string $content
     * @return bool
     */
    public static function detectXSS(string $content): bool
    {
        if (empty($content)) {
            return false;
        }

        foreach (self::XSS_PATTERNS as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        // Check for dangerous attributes
        foreach (self::DANGEROUS_ATTRIBUTES as $attr) {
            if (stripos($content, $attr) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate and sanitize rich text content (for Tiptap editor)
     *
     * @param string $content
     * @return string
     */
    public static function sanitizeRichText(string $content): string
    {
        if (empty($content)) {
            return $content;
        }

        // Log potential XSS attempts
        if (self::detectXSS($content)) {
            Log::warning('XSS Protection: Potential XSS detected in rich text', [
                'content' => substr($content, 0, 500),
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'user_id' => auth()->id()
            ]);
        }

        return self::cleanHtml($content);
    }

    /**
     * Escape output for safe display
     *
     * @param string $content
     * @param bool $allowBasicHtml
     * @return string
     */
    public static function escapeOutput(string $content, bool $allowBasicHtml = false): string
    {
        if (empty($content)) {
            return $content;
        }

        if ($allowBasicHtml) {
            return self::cleanHtml($content);
        }

        return htmlspecialchars($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    /**
     * Generate Content Security Policy header value
     *
     * @return string
     */
    public static function generateCSP(): string
    {
        $csp = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
            "font-src 'self' https://fonts.gstatic.com",
            "img-src 'self' data: https: blob:",
            "media-src 'self' https:",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
            "upgrade-insecure-requests"
        ];

        return implode('; ', $csp);
    }

    /**
     * Validate URL for safe redirects
     *
     * @param string $url
     * @return bool
     */
    public static function isValidRedirectUrl(string $url): bool
    {
        // Check for javascript: or data: schemes
        if (preg_match('/^(javascript|data|vbscript):/i', $url)) {
            return false;
        }

        // Allow relative URLs
        if (str_starts_with($url, '/') && !str_starts_with($url, '//')) {
            return true;
        }

        // Allow same domain URLs
        $parsedUrl = parse_url($url);
        if ($parsedUrl && isset($parsedUrl['host'])) {
            $allowedHosts = config('xss_protection.allowed_redirect_hosts', [
                request()->getHost()
            ]);
            
            return in_array($parsedUrl['host'], $allowedHosts);
        }

        return false;
    }
}
