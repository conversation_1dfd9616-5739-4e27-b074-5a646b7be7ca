<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class AuthorizationService
{
    /**
     * Role hierarchy - higher values have more permissions
     */
    const ROLE_HIERARCHY = [
        'user' => 1,
        'validator' => 2,
        'editor' => 3,
        'admin' => 4,
        'superadmin' => 5,
        'developer' => 6,
    ];

    /**
     * Protected Nova resources that require special permissions
     */
    const PROTECTED_RESOURCES = [
        'users' => ['user', 'validator', 'editor', 'admin', 'superadmin', 'developer'],
        'file-sources' => ['admin', 'superadmin', 'developer'],
        'contacts' => ['admin', 'superadmin', 'developer'],
        'news' => ['user', 'validator', 'editor', 'admin', 'superadmin', 'developer'],
    ];

    /**
     * Feature-based access control - defines which features require authentication/authorization
     */
    const FEATURE_ACCESS = [
        // Public features - no authentication required
        'public' => [
            'home' => true,
            'search' => true,
            'details' => true,
            'about' => true,
            'userguide' => true,
            'news.view' => true,
        ],

        // Features requiring authentication
        'authenticated' => [
            'contact.submit' => ['user', 'validator', 'editor', 'admin', 'superadmin', 'developer'],
        ],

        // Features requiring specific roles
        'role_based' => [
            'import' => ['admin', 'superadmin', 'developer'],
            'import.data' => ['admin', 'superadmin', 'developer'],
            'import.files' => ['admin', 'superadmin', 'developer'],
            'data.export' => ['editor', 'admin', 'superadmin', 'developer'],
            'data.management' => ['admin', 'superadmin', 'developer'],
            'news.manage' => ['editor', 'admin', 'superadmin', 'developer'],
            'contact.view' => ['admin', 'superadmin', 'developer'],
            'advanced.search' => ['user', 'validator', 'editor', 'admin', 'superadmin', 'developer'],
        ],

        // Admin-only features
        'admin_only' => [
            'system.import' => ['admin', 'superadmin', 'developer'],
            'file.management' => ['admin', 'superadmin', 'developer'],
            'user.management' => ['admin', 'superadmin', 'developer'],
        ]
    ];

    /**
     * Actions that require elevated permissions
     */
    const ELEVATED_ACTIONS = [
        'delete' => ['admin', 'superadmin', 'developer'],
        'forceDelete' => ['superadmin', 'developer'],
        'restore' => ['admin', 'superadmin', 'developer'],
        'replicate' => ['admin', 'superadmin', 'developer'],
    ];

    /**
     * Check if user has permission to access a resource
     *
     * @param string $resource
     * @param string $action
     * @param mixed $model
     * @return bool
     */
    public static function canAccess(string $resource, string $action = 'view', $model = null): bool
    {
        $user = Auth::user();
        
        if (!$user) {
            return false;
        }

        // Developers have access to everything
        if (self::hasRole($user, 'developer')) {
            return true;
        }

        // Check resource-specific permissions
        if (isset(self::PROTECTED_RESOURCES[$resource])) {
            $requiredRoles = self::PROTECTED_RESOURCES[$resource];
            if (!self::hasAnyRole($user, $requiredRoles)) {
                self::logUnauthorizedAccess($user, $resource, $action);
                return false;
            }
        }

        // Check action-specific permissions
        if (isset(self::ELEVATED_ACTIONS[$action])) {
            $requiredRoles = self::ELEVATED_ACTIONS[$action];
            if (!self::hasAnyRole($user, $requiredRoles)) {
                self::logUnauthorizedAccess($user, $resource, $action);
                return false;
            }
        }

        // Check model-specific permissions
        if ($model && method_exists($model, 'canBeAccessedBy')) {
            return $model->canBeAccessedBy($user, $action);
        }

        return true;
    }

    /**
     * Check if user can view Nova dashboard
     *
     * @return bool
     */
    public static function canAccessNova(): bool
    {
        $user = Auth::user();
        
        if (!$user) {
            return false;
        }

        // Only allow specific roles to access Nova
        $allowedRoles = ['validator','user', 'editor', 'admin', 'superadmin', 'developer'];
        return self::hasAnyRole($user, $allowedRoles);
    }

    /**
     * Check if user has a specific role
     *
     * @param mixed $user
     * @param string $role
     * @return bool
     */
    public static function hasRole($user, string $role): bool
    {
        if (!$user || !isset($user->role)) {
            return false;
        }

        return $user->role === $role;
    }

    /**
     * Check if user has any of the specified roles
     *
     * @param mixed $user
     * @param array $roles
     * @return bool
     */
    public static function hasAnyRole($user, array $roles): bool
    {
        if (!$user || !isset($user->role)) {
            return false;
        }

        return in_array($user->role, $roles);
    }

    /**
     * Check if user has role with minimum level
     *
     * @param mixed $user
     * @param string $minimumRole
     * @return bool
     */
    public static function hasMinimumRole($user, string $minimumRole): bool
    {
        if (!$user || !isset($user->role)) {
            return false;
        }

        $userLevel = self::ROLE_HIERARCHY[$user->role] ?? 0;
        $minimumLevel = self::ROLE_HIERARCHY[$minimumRole] ?? 0;

        return $userLevel >= $minimumLevel;
    }

    /**
     * Get user's role level
     *
     * @param mixed $user
     * @return int
     */
    public static function getRoleLevel($user): int
    {
        if (!$user || !isset($user->role)) {
            return 0;
        }

        return self::ROLE_HIERARCHY[$user->role] ?? 0;
    }

    /**
     * Check if user can perform action on specific model
     *
     * @param mixed $user
     * @param string $action
     * @param mixed $model
     * @return bool
     */
    public static function canPerformAction($user, string $action, $model = null): bool
    {
        if (!$user) {
            return false;
        }

        // Developers can do everything
        if (self::hasRole($user, 'developer')) {
            return true;
        }

        // Check if action requires elevated permissions
        if (isset(self::ELEVATED_ACTIONS[$action])) {
            return self::hasAnyRole($user, self::ELEVATED_ACTIONS[$action]);
        }

        // Check model-specific permissions
        if ($model) {
            // Users can only edit their own records (for certain models)
            if (in_array($action, ['update', 'view']) && 
                method_exists($model, 'belongsToUser') && 
                $model->belongsToUser($user)) {
                return true;
            }

            // Check if model has custom authorization logic
            if (method_exists($model, 'canBeModifiedBy')) {
                return $model->canBeModifiedBy($user, $action);
            }
        }

        // Default permissions based on role
        switch ($action) {
            case 'view':
            case 'index':
                return self::hasMinimumRole($user, 'editor');
            
            case 'create':
            case 'update':
                return self::hasMinimumRole($user, 'editor');
            
            case 'delete':
                return self::hasMinimumRole($user, 'admin');
            
            case 'forceDelete':
            case 'restore':
                return self::hasMinimumRole($user, 'superadmin');
            
            default:
                return self::hasMinimumRole($user, 'admin');
        }
    }

    /**
     * Log unauthorized access attempts
     *
     * @param mixed $user
     * @param string $resource
     * @param string $action
     * @return void
     */
    private static function logUnauthorizedAccess($user, string $resource, string $action): void
    {
        Log::warning('Unauthorized access attempt', [
            'user_id' => $user->id ?? null,
            'user_role' => $user->role ?? null,
            'resource' => $resource,
            'action' => $action,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'timestamp' => now()
        ]);
    }

    /**
     * Check session security
     *
     * @param Request $request
     * @return bool
     */
    public static function isSessionSecure(Request $request): bool
    {
        $user = Auth::user();
        
        if (!$user) {
            return false;
        }

        // Check for session hijacking indicators
        $sessionData = $request->session()->all();
        
        // Verify IP consistency (if enabled)
        if (config('auth.verify_ip', false)) {
            $sessionIp = $request->session()->get('user_ip');
            if ($sessionIp && $sessionIp !== $request->ip()) {
                Log::warning('Session IP mismatch detected', [
                    'user_id' => $user->id,
                    'session_ip' => $sessionIp,
                    'current_ip' => $request->ip()
                ]);
                return false;
            }
        }

        // Verify user agent consistency
        $sessionUserAgent = $request->session()->get('user_agent');
        if ($sessionUserAgent && $sessionUserAgent !== $request->userAgent()) {
            Log::warning('Session User-Agent mismatch detected', [
                'user_id' => $user->id,
                'session_ua' => $sessionUserAgent,
                'current_ua' => $request->userAgent()
            ]);
            return false;
        }

        return true;
    }

    /**
     * Initialize secure session
     *
     * @param Request $request
     * @return void
     */
    public static function initializeSecureSession(Request $request): void
    {
        $request->session()->put('user_ip', $request->ip());
        $request->session()->put('user_agent', $request->userAgent());
        $request->session()->put('login_time', now());
    }

    /**
     * Check if user session should be terminated
     *
     * @param Request $request
     * @return bool
     */
    public static function shouldTerminateSession(Request $request): bool
    {
        $user = Auth::user();
        
        if (!$user) {
            return true;
        }

        // Check session timeout
        $loginTime = $request->session()->get('login_time');
        if ($loginTime) {
            $sessionTimeout = config('session.lifetime', 120) * 60; // Convert to seconds
            if (now()->diffInSeconds($loginTime) > $sessionTimeout) {
                return true;
            }
        }

        // Check for suspicious activity
        if (!self::isSessionSecure($request)) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can access a specific feature
     *
     * @param string $feature
     * @param mixed $user
     * @return bool
     */
    public static function canAccessFeature(string $feature, $user = null): bool
    {
        $user = $user ?? Auth::user();

        // Check if feature is public
        if (isset(self::FEATURE_ACCESS['public'][$feature]) && self::FEATURE_ACCESS['public'][$feature]) {
            return true;
        }

        // If user is not authenticated and feature is not public, deny access
        if (!$user) {
            return false;
        }

        // Developers have access to everything
        if (self::hasRole($user, 'developer')) {
            return true;
        }

        // Check authenticated features
        if (isset(self::FEATURE_ACCESS['authenticated'][$feature])) {
            $requiredRoles = self::FEATURE_ACCESS['authenticated'][$feature];
            return self::hasAnyRole($user, $requiredRoles);
        }

        // Check role-based features
        if (isset(self::FEATURE_ACCESS['role_based'][$feature])) {
            $requiredRoles = self::FEATURE_ACCESS['role_based'][$feature];
            return self::hasAnyRole($user, $requiredRoles);
        }

        // Check admin-only features
        if (isset(self::FEATURE_ACCESS['admin_only'][$feature])) {
            $requiredRoles = self::FEATURE_ACCESS['admin_only'][$feature];
            return self::hasAnyRole($user, $requiredRoles);
        }

        // Default: if feature is not defined, require authentication
        return $user !== null;
    }

    /**
     * Get required roles for a feature
     *
     * @param string $feature
     * @return array|null
     */
    public static function getRequiredRoles(string $feature): ?array
    {
        // Check all feature categories
        foreach (self::FEATURE_ACCESS as $category => $features) {
            if ($category === 'public') {
                continue; // Public features don't have role requirements
            }

            if (isset($features[$feature])) {
                return $features[$feature];
            }
        }

        return null;
    }

    /**
     * Check if a feature is public (no authentication required)
     *
     * @param string $feature
     * @return bool
     */
    public static function isPublicFeature(string $feature): bool
    {
        return isset(self::FEATURE_ACCESS['public'][$feature]) && self::FEATURE_ACCESS['public'][$feature];
    }

    /**
     * Check if a feature requires authentication
     *
     * @param string $feature
     * @return bool
     */
    public static function requiresAuthentication(string $feature): bool
    {
        return !self::isPublicFeature($feature);
    }

    /**
     * Log feature access attempt
     *
     * @param string $feature
     * @param bool $granted
     * @param mixed $user
     * @return void
     */
    public static function logFeatureAccess(string $feature, bool $granted, $user = null): void
    {
        $user = $user ?? Auth::user();

        Log::info('Feature access attempt', [
            'feature' => $feature,
            'access_granted' => $granted,
            'user_id' => $user->id ?? null,
            'user_role' => $user->role ?? null,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'timestamp' => now()
        ]);
    }
}
