<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RouteNotFoundTest extends TestCase
{
    /**
     * Test that accessing an unregistered route returns 404 instead of 500 error.
     *
     * @return void
     */
    public function test_unregistered_route_returns_404()
    {
        $response = $this->get('/conta');
        
        $response->assertStatus(404);
    }

    /**
     * Test that accessing an unregistered Arabic route returns 404 instead of 500 error.
     *
     * @return void
     */
    public function test_unregistered_arabic_route_returns_404()
    {
        $response = $this->get('/ar/conta');
        
        $response->assertStatus(404);
    }

    /**
     * Test that the 404 page loads without errors and contains expected content.
     *
     * @return void
     */
    public function test_404_page_loads_correctly()
    {
        $response = $this->get('/nonexistent-page');
        
        $response->assertStatus(404);
        $response->assertSee('404');
        $response->assertSee("La page demandée n'a pas pu être trouvée");
    }

    /**
     * Test that language switcher works on 404 pages without throwing errors.
     *
     * @return void
     */
    public function test_language_switcher_works_on_404_page()
    {
        $response = $this->get('/nonexistent-page');
        
        $response->assertStatus(404);
        // Check that the page contains language switcher links
        $response->assertSee('العربية');
        $response->assertSee('English');
        // Ensure no PHP errors are present in the response
        $response->assertDontSee('Call to a member function parameters() on null');
        $response->assertDontSee('ErrorException');
    }

    /**
     * Test that valid routes still work correctly.
     *
     * @return void
     */
    public function test_valid_routes_still_work()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
    }

    /**
     * Test that valid Arabic routes still work correctly.
     *
     * @return void
     */
    public function test_valid_arabic_routes_still_work()
    {
        $response = $this->get('/ar/');
        
        $response->assertStatus(200);
    }
}
