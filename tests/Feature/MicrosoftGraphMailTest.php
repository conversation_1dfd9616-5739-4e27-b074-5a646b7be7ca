<?php

namespace Tests\Feature;

use App\Mail\ExampleMicrosoftGraphMail;
use App\Services\MicrosoftGraphMailService;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class MicrosoftGraphMailTest extends TestCase
{
    /**
     * Test that the Microsoft Graph mail service can be instantiated
     */
    public function test_microsoft_graph_service_can_be_instantiated()
    {
        $service = app(MicrosoftGraphMailService::class);
        
        $this->assertInstanceOf(MicrosoftGraphMailService::class, $service);
    }

    /**
     * Test that the microsoft-graph mailer can be created
     */
    public function test_microsoft_graph_mailer_can_be_created()
    {
        $mailer = Mail::mailer('microsoft-graph');
        
        $this->assertInstanceOf(\Illuminate\Mail\Mailer::class, $mailer);
    }

    /**
     * Test that the example mailable can be instantiated and built
     */
    public function test_example_mailable_can_be_built()
    {
        $user = (object) ['name' => 'Test User', 'email' => '<EMAIL>'];
        $mailable = new ExampleMicrosoftGraphMail($user, 'Test message');
        
        $built = $mailable->build();
        
        $this->assertEquals('Example Email via Microsoft Graph', $built->subject);
        $this->assertEquals('emails.example-microsoft-graph', $built->view);
    }

    /**
     * Test that the service returns null token when credentials are not configured
     */
    public function test_service_returns_null_token_without_credentials()
    {
        // Clear any existing configuration
        config([
            'services.microsoft_graph.tenant_id' => null,
            'services.microsoft_graph.client_id' => null,
            'services.microsoft_graph.client_secret' => null,
        ]);

        $service = new MicrosoftGraphMailService();
        $token = $service->getAccessToken();
        
        $this->assertNull($token);
    }

    /**
     * Test that the service test connection fails without credentials
     */
    public function test_service_test_connection_fails_without_credentials()
    {
        // Clear any existing configuration
        config([
            'services.microsoft_graph.tenant_id' => null,
            'services.microsoft_graph.client_id' => null,
            'services.microsoft_graph.client_secret' => null,
        ]);

        $service = new MicrosoftGraphMailService();
        $result = $service->testConnection();
        
        $this->assertFalse($result);
    }

    /**
     * Test that the mail configuration includes microsoft-graph driver
     */
    public function test_mail_configuration_includes_microsoft_graph()
    {
        $mailers = config('mail.mailers');
        
        $this->assertArrayHasKey('microsoft-graph', $mailers);
        $this->assertEquals('microsoft-graph', $mailers['microsoft-graph']['transport']);
    }

    /**
     * Test that the services configuration includes microsoft_graph
     */
    public function test_services_configuration_includes_microsoft_graph()
    {
        $services = config('services');
        
        $this->assertArrayHasKey('microsoft_graph', $services);
        $this->assertArrayHasKey('tenant_id', $services['microsoft_graph']);
        $this->assertArrayHasKey('client_id', $services['microsoft_graph']);
        $this->assertArrayHasKey('client_secret', $services['microsoft_graph']);
    }

    /**
     * Test email data formatting
     */
    public function test_email_data_formatting()
    {
        $service = new MicrosoftGraphMailService();
        
        $emailData = [
            'to' => [['email' => '<EMAIL>', 'name' => 'Test User']],
            'subject' => 'Test Subject',
            'body' => '<h1>Test Body</h1>',
            'body_type' => 'HTML'
        ];

        // Use reflection to test the protected method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('formatEmailData');
        $method->setAccessible(true);
        
        $result = $method->invoke($service, $emailData);
        
        $this->assertArrayHasKey('message', $result);
        $this->assertEquals('Test Subject', $result['message']['subject']);
        $this->assertEquals('HTML', $result['message']['body']['contentType']);
        $this->assertEquals('<h1>Test Body</h1>', $result['message']['body']['content']);
        $this->assertCount(1, $result['message']['toRecipients']);
        $this->assertEquals('<EMAIL>', $result['message']['toRecipients'][0]['emailAddress']['address']);
    }
}
