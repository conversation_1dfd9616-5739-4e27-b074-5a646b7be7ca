<?php

namespace Tests\Unit;

use App\Models\User;
use App\Services\AuthorizationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class AuthorizationServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test role hierarchy
     */
    public function test_role_hierarchy()
    {
        $user = new User(['role' => 'admin']);
        
        $this->assertTrue(AuthorizationService::hasRole($user, 'admin'));
        $this->assertFalse(AuthorizationService::hasRole($user, 'superadmin'));
        $this->assertTrue(AuthorizationService::hasMinimumRole($user, 'editor'));
        $this->assertFalse(AuthorizationService::hasMinimumRole($user, 'superadmin'));
    }

    /**
     * Test Nova access permissions
     */
    public function test_nova_access_permissions()
    {
        // Test user without role
        $userWithoutRole = new User(['role' => 'user']);
        $this->actingAs($userWithoutRole);
        $this->assertFalse(AuthorizationService::canAccessNova());

        // Test editor
        $editor = new User(['role' => 'editor']);
        $this->actingAs($editor);
        $this->assertTrue(AuthorizationService::canAccessNova());

        // Test admin
        $admin = new User(['role' => 'admin']);
        $this->actingAs($admin);
        $this->assertTrue(AuthorizationService::canAccessNova());

        // Test developer
        $developer = new User(['role' => 'developer']);
        $this->actingAs($developer);
        $this->assertTrue(AuthorizationService::canAccessNova());
    }

    /**
     * Test resource access permissions
     */
    public function test_resource_access_permissions()
    {
        // Test user access to protected resource
        $user = new User(['role' => 'user']);
        $this->actingAs($user);
        $this->assertFalse(AuthorizationService::canAccess('users', 'view'));

        // Test admin access to protected resource
        $admin = new User(['role' => 'admin']);
        $this->actingAs($admin);
        $this->assertTrue(AuthorizationService::canAccess('users', 'view'));

        // Test developer access (should have access to everything)
        $developer = new User(['role' => 'developer']);
        $this->actingAs($developer);
        $this->assertTrue(AuthorizationService::canAccess('users', 'delete'));
        $this->assertTrue(AuthorizationService::canAccess('any-resource', 'any-action'));
    }

    /**
     * Test elevated action permissions
     */
    public function test_elevated_action_permissions()
    {
        // Test editor trying to delete (should fail)
        $editor = new User(['role' => 'editor']);
        $this->actingAs($editor);
        $this->assertFalse(AuthorizationService::canAccess('news', 'delete'));

        // Test admin deleting (should succeed)
        $admin = new User(['role' => 'admin']);
        $this->actingAs($admin);
        $this->assertTrue(AuthorizationService::canAccess('news', 'delete'));

        // Test force delete (only superadmin and developer)
        $this->assertFalse(AuthorizationService::canAccess('news', 'forceDelete'));
        
        $superadmin = new User(['role' => 'superadmin']);
        $this->actingAs($superadmin);
        $this->assertTrue(AuthorizationService::canAccess('news', 'forceDelete'));
    }

    /**
     * Test role checking methods
     */
    public function test_role_checking_methods()
    {
        $admin = new User(['role' => 'admin']);

        $this->assertTrue(AuthorizationService::hasRole($admin, 'admin'));
        $this->assertFalse(AuthorizationService::hasRole($admin, 'editor'));

        $this->assertTrue(AuthorizationService::hasAnyRole($admin, ['admin', 'superadmin']));
        $this->assertFalse(AuthorizationService::hasAnyRole($admin, ['user', 'editor']));

        $this->assertEquals(3, AuthorizationService::getRoleLevel($admin));
    }

    /**
     * Test action permissions
     */
    public function test_action_permissions()
    {
        $editor = new User(['role' => 'editor']);
        $admin = new User(['role' => 'admin']);
        $superadmin = new User(['role' => 'superadmin']);

        // View permissions
        $this->assertTrue(AuthorizationService::canPerformAction($editor, 'view'));
        $this->assertTrue(AuthorizationService::canPerformAction($admin, 'view'));

        // Create permissions
        $this->assertTrue(AuthorizationService::canPerformAction($editor, 'create'));
        $this->assertTrue(AuthorizationService::canPerformAction($admin, 'create'));

        // Delete permissions
        $this->assertFalse(AuthorizationService::canPerformAction($editor, 'delete'));
        $this->assertTrue(AuthorizationService::canPerformAction($admin, 'delete'));

        // Force delete permissions
        $this->assertFalse(AuthorizationService::canPerformAction($admin, 'forceDelete'));
        $this->assertTrue(AuthorizationService::canPerformAction($superadmin, 'forceDelete'));
    }

    /**
     * Test session security
     */
    public function test_session_security()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $this->actingAs($user);

        $request = Request::create('/test', 'GET');

        // Start session for testing
        $this->startSession();
        $request->setLaravelSession($this->app['session.store']);

        // Initialize secure session
        AuthorizationService::initializeSecureSession($request);

        // Should be secure initially
        $this->assertTrue(AuthorizationService::isSessionSecure($request));

        // Test with different IP (if IP verification is enabled)
        if (config('auth.verify_ip', false)) {
            $request->session()->put('user_ip', '***********');
            $request->server->set('REMOTE_ADDR', '***********');
            $this->assertFalse(AuthorizationService::isSessionSecure($request));
        }
    }

    /**
     * Test session termination conditions
     */
    public function test_session_termination()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $this->actingAs($user);

        $request = Request::create('/test', 'GET');

        // Start session for testing
        $this->startSession();
        $request->setLaravelSession($this->app['session.store']);

        // Fresh session should not be terminated
        AuthorizationService::initializeSecureSession($request);
        $this->assertFalse(AuthorizationService::shouldTerminateSession($request));

        // Test with old login time
        $request->session()->put('login_time', now()->subHours(5));
        $this->assertTrue(AuthorizationService::shouldTerminateSession($request));
    }

    /**
     * Test user without authentication
     */
    public function test_unauthenticated_user()
    {
        // No user authenticated
        $this->assertFalse(AuthorizationService::canAccessNova());
        $this->assertFalse(AuthorizationService::canAccess('users', 'view'));
        $this->assertFalse(AuthorizationService::canPerformAction(null, 'view'));
    }

    /**
     * Test user with null role
     */
    public function test_user_with_null_role()
    {
        $userWithNullRole = new User(['role' => null]);
        $this->actingAs($userWithNullRole);

        $this->assertFalse(AuthorizationService::canAccessNova());
        $this->assertFalse(AuthorizationService::hasRole($userWithNullRole, 'admin'));
        $this->assertEquals(0, AuthorizationService::getRoleLevel($userWithNullRole));
    }

    /**
     * Test protected resources configuration
     */
    public function test_protected_resources_configuration()
    {
        $editor = new User(['role' => 'editor']);
        $admin = new User(['role' => 'admin']);

        $this->actingAs($editor);
        
        // Editor should not access users resource
        $this->assertFalse(AuthorizationService::canAccess('users', 'view'));
        
        // But should access news resource
        $this->assertTrue(AuthorizationService::canAccess('news', 'view'));

        $this->actingAs($admin);
        
        // Admin should access both
        $this->assertTrue(AuthorizationService::canAccess('users', 'view'));
        $this->assertTrue(AuthorizationService::canAccess('news', 'view'));
    }
}
