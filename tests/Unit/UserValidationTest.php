<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\Validator;

class UserValidationTest extends TestCase
{
    /** @test */
    public function it_validates_name_correctly()
    {
        // Valid names (now more lenient - allows numbers, special chars, etc.)
        $validNames = [
            '<PERSON>',
            'محمد أحمد',
            '<PERSON>',
            'يوسف',
            '<PERSON>',
            'أحمد محمد علي',
            'Test123',                          // Now allowed - contains numbers
            '<PERSON>@Doe',                         // Now allowed - contains special characters
            'User_Name',                        // Now allowed - contains underscore
            'Test-Name',                        // Now allowed - contains dash
            'Company Ltd.',                     // Now allowed - business names
            'Dr. <PERSON>',                        // Now allowed - titles
        ];

        foreach ($validNames as $name) {
            $validator = Validator::make(['name' => $name], [
                'name' => ['required', 'max:255', 'min:2']
            ]);
            $this->assertTrue($validator->passes(), "Name '$name' should be valid");
        }

        // Invalid names (only XSS and too short)
        $invalidNames = [
            '<script>alert(1)</script>',        // XSS attempt
            'a',                                // Too short
            '',                                 // Empty
        ];

        foreach ($invalidNames as $name) {
            if ($name === '<script>alert(1)</script>') {
                // For XSS, we need to test with the actual XSS detection
                $this->assertTrue(\App\Services\XSSProtectionService::detectXSS($name), "XSS should be detected in '$name'");
            } else {
                $validator = Validator::make(['name' => $name], [
                    'name' => ['required', 'max:255', 'min:2']
                ]);
                $this->assertTrue($validator->fails(), "Name '$name' should be invalid");
            }
        }
    }

    /** @test */
    public function it_validates_password_correctly()
    {
        // Valid passwords (very lenient - just minimum length, no XSS check since passwords are hashed)
        $validPasswords = [
            'Password123!',
            'MySecure@Pass1',
            'StrongPass#456',
            'Complex$Pass789',
            'Secure.Pass123',
            'password',                 // Simple passwords allowed
            'PASSWORD123',              // No special chars required
            'simple123',                // Simple passwords allowed
            'mypassword',               // Just letters allowed
            '123456',                   // Just numbers allowed
            '<script>alert(1)</script>', // Even this is allowed since it gets hashed
        ];

        foreach ($validPasswords as $password) {
            $validator = Validator::make(['password' => $password], [
                'password' => ['required', 'string', 'min:6']
            ]);
            $this->assertTrue($validator->passes(), "Password '$password' should be valid");
        }

        // Invalid passwords (only too short - no XSS check since passwords are hashed)
        $invalidPasswords = [
            '12345',                            // Too short
            'abc',                              // Too short
            '',                                 // Empty
        ];

        foreach ($invalidPasswords as $password) {
            $validator = Validator::make(['password' => $password], [
                'password' => ['required', 'string', 'min:6']
            ]);
            $this->assertTrue($validator->fails(), "Password '$password' should be invalid");
        }
    }


}
