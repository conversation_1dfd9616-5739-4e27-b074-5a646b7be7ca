<?php

namespace Tests\Unit;

use App\Services\FileValidationService;
use Illuminate\Http\UploadedFile;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

class FileValidationServiceTest extends TestCase
{
    /**
     * Test that allowed file types pass validation
     */
    public function test_allowed_file_types_pass_validation()
    {
        // Create a fake image file
        $file = UploadedFile::fake()->image('test.jpg', 100, 100)->size(1024); // 1MB

        // This should not throw an exception
        $this->expectNotToPerformAssertions();
        FileValidationService::validateFile($file, [
            'allowed_extensions' => ['jpg'],
            'allowed_mime_types' => ['image/jpeg'],
            'max_size' => 2 * 1024 * 1024 // 2MB
        ]);
    }

    /**
     * Test that dangerous file extensions are rejected
     */
    public function test_dangerous_file_extensions_are_rejected()
    {
        // Create a fake PHP file
        $file = UploadedFile::fake()->create('malicious.php', 1024);

        $this->expectException(ValidationException::class);
        FileValidationService::validateFile($file);
    }

    /**
     * Test that files exceeding size limit are rejected
     */
    public function test_oversized_files_are_rejected()
    {
        // Create a large fake file
        $file = UploadedFile::fake()->create('large.txt', 60 * 1024); // 60MB

        $this->expectException(ValidationException::class);
        FileValidationService::validateFile($file, [
            'max_size' => 50 * 1024 * 1024 // 50MB limit
        ]);
    }

    /**
     * Test that Trix file validation works
     */
    public function test_trix_file_validation()
    {
        // Create a fake image file for Trix
        $file = UploadedFile::fake()->image('trix-image.png', 200, 200)->size(5 * 1024); // 5MB

        // This should not throw an exception
        $this->expectNotToPerformAssertions();
        FileValidationService::validateTrixFile($file);
    }

    /**
     * Test that validation rules are generated correctly
     */
    public function test_validation_rules_generation()
    {
        $rules = FileValidationService::getValidationRules();

        $this->assertIsArray($rules);
        $this->assertContains('required', $rules);
        $this->assertContains('file', $rules);
        
        // Check that mimes rule exists
        $mimesRule = collect($rules)->first(function ($rule) {
            return is_string($rule) && str_starts_with($rule, 'mimes:');
        });
        $this->assertNotNull($mimesRule);

        // Check that max rule exists
        $maxRule = collect($rules)->first(function ($rule) {
            return is_string($rule) && str_starts_with($rule, 'max:');
        });
        $this->assertNotNull($maxRule);
    }

    /**
     * Test that Trix validation rules are generated correctly
     */
    public function test_trix_validation_rules_generation()
    {
        $rules = FileValidationService::getTrixValidationRules();

        $this->assertIsArray($rules);
        $this->assertContains('required', $rules);
        $this->assertContains('file', $rules);
    }

    /**
     * Test configuration methods
     */
    public function test_configuration_methods()
    {
        $allowedExtensions = FileValidationService::getAllowedExtensions();
        $this->assertIsArray($allowedExtensions);
        $this->assertNotEmpty($allowedExtensions);

        $dangerousExtensions = FileValidationService::getDangerousExtensions();
        $this->assertIsArray($dangerousExtensions);
        $this->assertContains('php', $dangerousExtensions);

        $allowedMimeTypes = FileValidationService::getAllowedMimeTypes();
        $this->assertIsArray($allowedMimeTypes);
        $this->assertNotEmpty($allowedMimeTypes);

        $dangerousMimeTypes = FileValidationService::getDangerousMimeTypes();
        $this->assertIsArray($dangerousMimeTypes);
        $this->assertContains('application/x-php', $dangerousMimeTypes);

        $maxSize = FileValidationService::getMaxFileSize();
        $this->assertIsInt($maxSize);
        $this->assertGreaterThan(0, $maxSize);
    }
}
