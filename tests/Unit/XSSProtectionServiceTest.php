<?php

namespace Tests\Unit;

use App\Services\XSSProtectionService;
use Tests\TestCase;

class XSSProtectionServiceTest extends TestCase
{
    /**
     * Test XSS detection in malicious content
     */
    public function test_detects_xss_in_malicious_content()
    {
        $maliciousContent = [
            '<script>alert("XSS")</script>',
            '<img src="x" onerror="alert(1)">',
            'javascript:alert("XSS")',
            '<iframe src="javascript:alert(1)"></iframe>',
            '<div onclick="alert(1)">Click me</div>',
            '<a href="javascript:void(0)" onclick="alert(1)">Link</a>',
            '<style>body{background:url("javascript:alert(1)")}</style>',
            '<object data="data:text/html,<script>alert(1)</script>"></object>'
        ];

        foreach ($maliciousContent as $content) {
            $this->assertTrue(
                XSSProtectionService::detectXSS($content),
                "Failed to detect XSS in: {$content}"
            );
        }
    }

    /**
     * Test that safe content is not flagged as XSS
     */
    public function test_safe_content_not_flagged_as_xss()
    {
        $safeContent = [
            'This is a normal text',
            '<p>This is a paragraph</p>',
            '<strong>Bold text</strong>',
            '<a href="https://example.com">Safe link</a>',
            '<img src="image.jpg" alt="Safe image">',
            'Email: <EMAIL>',
            'Phone: +1234567890'
        ];

        foreach ($safeContent as $content) {
            $this->assertFalse(
                XSSProtectionService::detectXSS($content),
                "Safe content incorrectly flagged as XSS: {$content}"
            );
        }
    }

    /**
     * Test HTML cleaning functionality
     */
    public function test_html_cleaning()
    {
        $testCases = [
            [
                'input' => '<script>alert("XSS")</script><p>Safe content</p>',
                'expected_contains' => 'Safe content',
                'expected_not_contains' => '<script>'
            ],
            [
                'input' => '<p onclick="alert(1)">Click me</p>',
                'expected_contains' => 'Click me',
                'expected_not_contains' => 'onclick'
            ],
            [
                'input' => '<img src="x" onerror="alert(1)" alt="Image">',
                'expected_contains' => 'alt="Image"',
                'expected_not_contains' => 'onerror'
            ]
        ];

        foreach ($testCases as $case) {
            $cleaned = XSSProtectionService::cleanHtml($case['input']);
            
            $this->assertStringContainsString(
                $case['expected_contains'],
                $cleaned,
                "Cleaned content should contain: {$case['expected_contains']}"
            );
            
            $this->assertStringNotContainsString(
                $case['expected_not_contains'],
                $cleaned,
                "Cleaned content should not contain: {$case['expected_not_contains']}"
            );
        }
    }

    /**
     * Test input sanitization with different options
     */
    public function test_input_sanitization()
    {
        $input = '<script>alert("XSS")</script><p>Safe <strong>content</strong></p>';

        // Test with HTML allowed
        $sanitizedWithHtml = XSSProtectionService::sanitizeInput($input, ['allow_html' => true]);
        $this->assertStringContainsString('<p>', $sanitizedWithHtml);
        $this->assertStringContainsString('<strong>', $sanitizedWithHtml);
        $this->assertStringNotContainsString('<script>', $sanitizedWithHtml);

        // Test with HTML not allowed
        $sanitizedWithoutHtml = XSSProtectionService::sanitizeInput($input, ['allow_html' => false]);
        $this->assertStringNotContainsString('<p>', $sanitizedWithoutHtml);
        $this->assertStringNotContainsString('<script>', $sanitizedWithoutHtml);

        // Test strict mode
        $sanitizedStrict = XSSProtectionService::sanitizeInput($input, ['strict_mode' => true]);
        $this->assertStringNotContainsString('<', $sanitizedStrict);
        $this->assertStringNotContainsString('>', $sanitizedStrict);
    }

    /**
     * Test rich text sanitization
     */
    public function test_rich_text_sanitization()
    {
        $richText = '<h1>Title</h1><p>Content with <script>alert("XSS")</script> and <strong>bold</strong> text</p>';
        
        $sanitized = XSSProtectionService::sanitizeRichText($richText);
        
        // Should keep safe HTML
        $this->assertStringContainsString('<h1>', $sanitized);
        $this->assertStringContainsString('<p>', $sanitized);
        $this->assertStringContainsString('<strong>', $sanitized);
        
        // Should remove dangerous content
        $this->assertStringNotContainsString('<script>', $sanitized);
        $this->assertStringNotContainsString('alert', $sanitized);
    }

    /**
     * Test array sanitization
     */
    public function test_array_sanitization()
    {
        $input = [
            'title' => '<script>alert("XSS")</script>Title',
            'description' => '<p>Safe content</p><script>alert("XSS")</script>',
            'nested' => [
                'field' => '<img src="x" onerror="alert(1)">'
            ]
        ];

        $sanitized = XSSProtectionService::sanitizeInput($input);

        $this->assertIsArray($sanitized);
        $this->assertStringNotContainsString('<script>', $sanitized['title']);
        $this->assertStringNotContainsString('<script>', $sanitized['description']);
        // The content is HTML escaped, so dangerous attributes are neutralized
        $this->assertStringNotContainsString('<img src="x" onerror=', $sanitized['nested']['field']);
    }

    /**
     * Test CSP generation
     */
    public function test_csp_generation()
    {
        $csp = XSSProtectionService::generateCSP();
        
        $this->assertIsString($csp);
        $this->assertStringContainsString("default-src 'self'", $csp);
        $this->assertStringContainsString("object-src 'none'", $csp);
        $this->assertStringContainsString("script-src", $csp);
        $this->assertStringContainsString("style-src", $csp);
    }

    /**
     * Test URL validation for redirects
     */
    public function test_url_validation()
    {
        // Valid URLs
        $validUrls = [
            '/relative/path',
            '/another-path',
            'https://islamicfinance.tn/page',
            'http://localhost/test'
        ];

        foreach ($validUrls as $url) {
            $this->assertTrue(
                XSSProtectionService::isValidRedirectUrl($url),
                "Valid URL incorrectly rejected: {$url}"
            );
        }

        // Invalid URLs
        $invalidUrls = [
            'javascript:alert(1)',
            'data:text/html,<script>alert(1)</script>',
            'vbscript:msgbox(1)',
            '//evil.com/redirect',
            'https://malicious.com/phishing'
        ];

        foreach ($invalidUrls as $url) {
            $this->assertFalse(
                XSSProtectionService::isValidRedirectUrl($url),
                "Invalid URL incorrectly accepted: {$url}"
            );
        }
    }

    /**
     * Test output escaping
     */
    public function test_output_escaping()
    {
        $input = '<script>alert("XSS")</script><p>Content</p>';
        
        // Test without HTML
        $escaped = XSSProtectionService::escapeOutput($input, false);
        $this->assertStringContainsString('&lt;script&gt;', $escaped);
        $this->assertStringContainsString('&lt;p&gt;', $escaped);

        // Test with basic HTML
        $escapedWithHtml = XSSProtectionService::escapeOutput($input, true);
        $this->assertStringNotContainsString('<script>', $escapedWithHtml);
        $this->assertStringContainsString('<p>', $escapedWithHtml);
    }

    /**
     * Test empty and null inputs
     */
    public function test_empty_inputs()
    {
        $this->assertEquals('', XSSProtectionService::sanitizeInput(''));
        $this->assertEquals('', XSSProtectionService::cleanHtml(''));
        $this->assertEquals('', XSSProtectionService::sanitizeRichText(''));
        $this->assertFalse(XSSProtectionService::detectXSS(''));
        
        $this->assertEquals('test', XSSProtectionService::sanitizeInput('test'));
    }
}
