<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MicrosoftGraphMailController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/



Route::group(['prefix' => 'ar/',  'middleware' => 'language'], function() {

Route::get('/', [\App\Http\Controllers\HomeController::class, 'index'])->name('index.ar');
Route::get('/search/{modelname}', [\App\Http\Controllers\HomeController::class, 'search'])->name('search.ar');
Route::get('/detail/{model}/{id}', [\App\Http\Controllers\HomeController::class, 'details'])->name('details.ar');
Route::get('/advancedsearch', [\App\Http\Controllers\HomeController::class, 'advancedsearchNew'])->name('advancedsearch.ar');
Route::get('/contact', [\App\Http\Controllers\HomeController::class, 'contact'])->name('contact.ar');
Route::post('/storecontact', [\App\Http\Controllers\HomeController::class, 'storecontact'])->name('storecontact.ar')->middleware('contact.protection');
Route::get('/import', [\App\Http\Controllers\API\AnnualInterConferenceController::class, 'selectfile'])->name('import.ar');
Route::get('/about', [\App\Http\Controllers\HomeController::class, 'aboutus'])->name('aboutus.ar');
Route::get('/aboutdatabase', [\App\Http\Controllers\HomeController::class, 'aboutdatabase'])->name('aboutdatabase.ar');
Route::get('/userguide', [\App\Http\Controllers\HomeController::class, 'userguide'])->name('userguide.ar');

Route::get('/fetch', [\App\Http\Controllers\FetchController::class, 'index'])->name('fetchindex.ar');
Route::get('/fetch/search/{modelname}', [\App\Http\Controllers\FetchController::class, 'search'])->name('fetchsearch.ar');
Route::get('/fetch/advancedsearch', [\App\Http\Controllers\FetchController::class, 'advancedsearch'])->name('fetchadvancedsearch.ar');
Route::get('/news', [\App\Http\Controllers\NewsController::class, 'index'])->name('news.ar');
Route::get('/news/{slug}', [\App\Http\Controllers\NewsController::class, 'show'])->name('detailnews.ar');
});

Route::get('/', [\App\Http\Controllers\HomeController::class, 'index'])->name('index.en');
Route::get('/search/{modelname}', [\App\Http\Controllers\HomeController::class, 'search'])->name('search.en');
Route::get('/detail/{model}/{id}', [\App\Http\Controllers\HomeController::class, 'details'])->name('details.en');
Route::get('/advancedsearch', [\App\Http\Controllers\HomeController::class, 'advancedsearchNew'])->name('advancedsearch.en');
Route::get('/contact', [\App\Http\Controllers\HomeController::class, 'contact'])->name('contact.en');
Route::post('/storecontact', [\App\Http\Controllers\HomeController::class, 'storecontact'])->name('storecontact.en')->middleware('contact.protection');
Route::get('/about', [\App\Http\Controllers\HomeController::class, 'aboutus'])->name('aboutus.en');
Route::get('/userguide', [\App\Http\Controllers\HomeController::class, 'userguide'])->name('userguide.en');

Route::get('/aboutdatabase', [\App\Http\Controllers\HomeController::class, 'aboutdatabase'])->name('aboutdatabase.en');

Route::get('/import', [\App\Http\Controllers\API\ImportarticleController::class, 'index'])->name('import.en');
Route::post('/updatedsourcefile', [\App\Http\Controllers\API\ImportarticleController::class, 'updatedsourcefile'])->name('updatedsourcefile');

//Route::get('/trucatefinancedatareset/financedata',[\App\Http\Controllers\HomeController::class, 'trucatefinancedata'])->name('trucatefinancedata');
// Route::get('/data/validateall',[\App\Http\Controllers\HomeController::class, 'validateall'])->name('validateall');
// Route::get('/data/unvalidateall',[\App\Http\Controllers\HomeController::class, 'unvalidateall'])->name('unvalidateall');


Route::get('/fetch', [\App\Http\Controllers\FetchController::class, 'index'])->name('fetchindex.en');
Route::get('/fetch/search/{modelname}', [\App\Http\Controllers\FetchController::class, 'search'])->name('fetchsearch.en');
Route::get('/fetch/advancedsearch', [\App\Http\Controllers\FetchController::class, 'advancedsearch'])->name('fetchadvancedsearch.en');
Route::get('/news', [\App\Http\Controllers\NewsController::class, 'index'])->name('news.en');
Route::get('/news/{slug}', [\App\Http\Controllers\NewsController::class, 'show'])->name('detailnews.en');

// Microsoft Graph Mail Testing Routes (for development/testing only)
Route::prefix('mail/microsoft-graph')->group(function () {
    Route::get('/test-connection', [MicrosoftGraphMailController::class, 'testConnection']);
    Route::post('/send-test-email', [MicrosoftGraphMailController::class, 'sendTestEmail']);
    Route::post('/send-direct-email', [MicrosoftGraphMailController::class, 'sendDirectEmail']);
});

// Route::get('/fi-phpinfo',function(){
//     phpinfo();
// });
