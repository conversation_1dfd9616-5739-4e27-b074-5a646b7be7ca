<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AnnualInterConferenceController;
use App\Http\Controllers\API\BookController;
use App\Http\Controllers\API\FilesourceController;
use App\Http\Controllers\API\InterConferenceController;
use App\Http\Controllers\API\InterConferenceRecommendationController;
use App\Http\Controllers\API\InterStandardController;
use App\Http\Controllers\API\JurisprudenceController;
use App\Http\Controllers\API\LegalBodyController;
use App\Http\Controllers\API\MagazineController;
use App\Http\Controllers\API\RegulationController;
use App\Http\Controllers\API\ReportController;
use App\Http\Controllers\API\ResearchController;
use App\Http\Controllers\API\UniversityNewsletterController;
use App\Models\Filesource;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});


Route::group(['middleware' => ['authapi']], function () {

    Route::post('annualinterconferences/store',         [AnnualInterConferenceController::class,'store'])           ->name('annualinterconferences.store');
    Route::post('books/store',                          [BookController::class,'store'])                            ->name('books.store');
    Route::post('interconferences/store',               [InterConferenceController::class,'store'])                 ->name('interconferences.store');
    Route::post('interconferencerecommendations/store', [InterConferenceRecommendationController::class,'store'])   ->name('interconferencerecommendations.store');
    Route::post('interstandards/store',                 [InterStandardController::class,'store'])                   ->name('interstandards.store');
    Route::post('jurisprudences/store',                 [JurisprudenceController::class,'store'])                   ->name('jurisprudences.store');
    Route::post('legalbodies/store',                    [LegalBodyController::class,'store'])                       ->name('legalbodies.store');
    Route::post('magazines/store',                      [MagazineController::class,'store'])                        ->name('magazines.store');
    Route::post('regulations/store',                    [RegulationController::class,'store'])                      ->name('regulations.store');
    Route::post('reports/store',                        [ReportController::class,'store'])                          ->name('reports.store');
    Route::post('researchs/store',                      [ResearchController::class,'store'])                        ->name('researchs.store');
    Route::post('universitynewsletters/store',          [UniversityNewsletterController::class,'store'])            ->name('universitynewsletters.store');
    Route::post('/imported-article-excelfile', [\App\Http\Controllers\API\ImportarticleController::class, 'store'])->name('importedarticleexcelfilestore');
    Route::post('update-file-status',                   [FilesourceController::class,'updatestatus'])               ->name('updatestatus');
    Route::post('update-file-comment',                   [FilesourceController::class,'updatecomment'])               ->name('updatescomment');

    // Secure file upload routes
    Route::post('secure-upload/file',                   [\App\Http\Controllers\API\SecureFileUploadController::class, 'uploadFile'])->name('secure.upload.file');
    Route::post('secure-upload/files',                  [\App\Http\Controllers\API\SecureFileUploadController::class, 'uploadMultipleFiles'])->name('secure.upload.files');
    Route::post('secure-upload/document',               [\App\Http\Controllers\API\SecureFileUploadController::class, 'uploadDocument'])->name('secure.upload.document');
    Route::post('secure-upload/image',                  [\App\Http\Controllers\API\SecureFileUploadController::class, 'uploadImage'])->name('secure.upload.image');
    Route::post('secure-upload/replace',                [\App\Http\Controllers\API\SecureFileUploadController::class, 'replaceFile'])->name('secure.upload.replace');
});
