# Microsoft Graph Mail Service Setup

This document explains how to set up and use the Microsoft Graph mail service in your Laravel application.

## Overview

The Microsoft Graph mail service provides a Laravel mail driver that uses Microsoft Graph API to send emails through Office 365/Outlook. This is particularly useful when your organization uses Microsoft Office 365 for email services.

## Features

- OAuth 2.0 authentication with Microsoft Graph
- Support for HTML and text emails
- Attachment support
- CC and BCC recipients
- Reply-to addresses
- Automatic token caching and refresh
- Error handling and logging
- Laravel mail interface compatibility

## Setup Instructions

### 1. Azure App Registration

First, you need to create an app registration in Azure Active Directory:

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to "Azure Active Directory" > "App registrations"
3. Click "New registration"
4. Fill in the application details:
   - Name: Your application name
   - Supported account types: Choose appropriate option
   - Redirect URI: Not needed for this setup
5. Click "Register"

### 2. Configure API Permissions

After creating the app registration:

1. Go to "API permissions"
2. Click "Add a permission"
3. Select "Microsoft Graph"
4. Choose "Application permissions"
5. Add the following permissions:
   - `Mail.Send` - Send mail as any user
   - `User.Read.All` - Read all users' profiles (optional, for testing)
6. Click "Grant admin consent"

### 3. Create Client Secret

1. Go to "Certificates & secrets"
2. Click "New client secret"
3. Add a description and set expiration
4. Copy the secret value (you won't be able to see it again)

### 4. Environment Configuration

Add the following variables to your `.env` file:

```env
# Set the mail driver to use Microsoft Graph
MAIL_MAILER=microsoft-graph

# Microsoft Graph Configuration
MICROSOFT_GRAPH_TENANT_ID=your-tenant-id
MICROSOFT_GRAPH_CLIENT_ID=your-client-id
MICROSOFT_GRAPH_CLIENT_SECRET=your-client-secret

# Set the from address to a valid Office 365 email
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Application Name"
```

You can find these values in your Azure app registration:
- **Tenant ID**: Directory (tenant) ID in the Overview section
- **Client ID**: Application (client) ID in the Overview section
- **Client Secret**: The secret you created

## Usage Examples

### Basic Usage with Mailable

```php
use App\Mail\ExampleMicrosoftGraphMail;
use Illuminate\Support\Facades\Mail;

// Send email using the mailable class
Mail::to('<EMAIL>')->send(new ExampleMicrosoftGraphMail($user, 'Hello World!'));
```

### Direct Mail Facade Usage

```php
use Illuminate\Support\Facades\Mail;

Mail::send('emails.example', ['data' => $data], function ($message) {
    $message->to('<EMAIL>')
            ->subject('Test Email via Microsoft Graph');
});
```

### Using Specific Mailer

```php
use Illuminate\Support\Facades\Mail;

Mail::mailer('microsoft-graph')
    ->to('<EMAIL>')
    ->send(new ExampleMicrosoftGraphMail($user, 'Hello World!'));
```

### Queue Support

The Microsoft Graph mail driver works with Laravel's queue system:

```php
use App\Mail\ExampleMicrosoftGraphMail;
use Illuminate\Support\Facades\Mail;

// Queue the email
Mail::to('<EMAIL>')->queue(new ExampleMicrosoftGraphMail($user, 'Hello World!'));
```

## Testing the Connection

You can test the Microsoft Graph connection using the service directly:

```php
use App\Services\MicrosoftGraphMailService;

$service = app(MicrosoftGraphMailService::class);

if ($service->testConnection()) {
    echo "Microsoft Graph connection successful!";
} else {
    echo "Microsoft Graph connection failed!";
}
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify your tenant ID, client ID, and client secret
   - Ensure the app registration has the correct permissions
   - Check that admin consent has been granted

2. **Permission Denied**
   - Verify the `Mail.Send` permission is granted
   - Ensure admin consent is provided for application permissions

3. **Invalid From Address**
   - The from address must be a valid Office 365 mailbox
   - The application must have permission to send as that user

### Logging

The service logs all operations. Check your Laravel logs for detailed error information:

```bash
tail -f storage/logs/laravel.log
```

## Security Considerations

- Store client secrets securely and never commit them to version control
- Use environment variables for all sensitive configuration
- Regularly rotate client secrets
- Monitor API usage and permissions
- Consider using certificate-based authentication for production

## API Rate Limits

Microsoft Graph has rate limits. The service includes basic retry logic, but for high-volume applications, consider:

- Implementing exponential backoff
- Using queue workers to spread load
- Monitoring rate limit headers
- Implementing circuit breaker patterns

## Support

For issues related to:
- **Laravel integration**: Check Laravel documentation and logs
- **Microsoft Graph API**: Refer to Microsoft Graph documentation
- **Azure configuration**: Check Azure Active Directory documentation
